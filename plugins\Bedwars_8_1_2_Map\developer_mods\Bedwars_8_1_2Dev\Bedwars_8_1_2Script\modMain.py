# -*- coding: utf-8 -*-

from common.mod import Mod
import server.extraServerApi as serverApi

@Mod.Binding(name='Bedwars_8_1_2Mod', version='1.0.0')
class Bedwars_8_1_2Server(object):
    
    @Mod.InitServer()
    def Bedwars_8_1_2ServerInit(self):
        serverApi.RegisterSystem('Bedwars_8_1_2Mod', 'Bedwars_8_1_2ServerSystem', 'Bedwars_8_1_2Script.Bedwars_8_1_2ServerSystem.Bedwars_8_1_2ServerSystem')

    @Mod.DestroyServer()
    def Bedwars_8_1_2Server<PERSON><PERSON>roy(self):
        pass
