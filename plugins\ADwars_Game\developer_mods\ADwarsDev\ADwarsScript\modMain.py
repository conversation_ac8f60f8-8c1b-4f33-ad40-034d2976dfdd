# -*- coding: utf-8 -*-

from common.mod import Mod
import server.extraServerApi as serverApi

@Mod.Binding(name='ADwarsMod', version='1.0.0')
class ADwarsServer(object):
    
    @Mod.InitServer()
    def ADwarsServerInit(self):
        serverApi.RegisterSystem('ADwarsMod', 'ADwarsServerSystem', 'ADwarsScript.ADwarsServerSystem.ADwarsServerSystem')

    @Mod.DestroyServer()
    def ADwarsServerDestroy(self):
        pass
