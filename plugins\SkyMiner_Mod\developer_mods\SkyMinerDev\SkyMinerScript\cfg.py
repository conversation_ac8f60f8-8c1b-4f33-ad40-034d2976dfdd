# -*- coding: utf-8 -*-
spawn_pos = [0.5, 66, 0.5]

# 一个区域不规则形状拆分若干长方体
spawn_island_area = [
    [-17, 0, -29, 26, 120, 28],
    [-30, 0, -29, -17, 120, 13],
    [26, 0, -30, 33, 120, 16],
    [443, -17, 469, 458, 90, 480]
]

refill_blocks = {
    # "minecraft:anvil": [
    #     [-14, 71, 13],
    #     [-14, 71, 0],
    #     [6, 71, -31],
    #     [0, 71, -43],
    #     [10040, -25, 10100],
    #     [10040, -25, 10079]
    # ],
}

exp_points = [
    [5, 62, 66],
    [7, 62, 64],
    [5, 62, 62],
    [3, 62, 64],

    [7, 62, 62],
    [7, 62, 66],
    [3, 62, 62],
    [3, 62, 66],

]

exp_island_area = [-11, 59, 48, 21, 70, 80]

exp_area = [-4, 60, 55, 14, 70, 73]

exp_glass_pos = [5, 63, 64]
# stained_glass_pane 5 14

# 出生岛原木点
spawn_island_log_area = [-16, 63, -5, -13, 65, -2]

# 主岛资源点，每行代表一个单独的资源点
ore_areas = [
    [-57, 61, 34, -48, 66, 43],
    [-57, 61, 81, -50, 66, 88],
    [-49, 61, 122, -41, 66, 130],
    [-7, 61, 116, 2, 66, 125],
    [10, 61, 102, 19, 66, 111],
    [55, 62, 85, 63, 67, 93],
    [44, 62, 35, 52, 67, 43]
]

# 地狱资源点
ore_areas1 = [
    [481, -16, 443, 486, -11, 448],
    [511, -16, 443, 516, -11, 448],
    [511, -16, 501, 516, -11, 506],
    [481, -16, 501, 486, -11, 506],
    [496, -16, 472, 501, -11, 477],

]

# 地狱出生点
spawn_pos1 = [446.5, -13, 474.5]


refetch_time = 60 * 4

# 主岛资源点矿物分布
main_ore_blocks = [
    {
        "minecraft:diamond_ore": 20,
        "minecraft:diamond_block": 1,
        "minecraft:cobblestone": 60,
        "minecraft:stone": 60,
        "minecraft:cobbled_deepslate": 60,
        "minecraft:deepslate_diamond_ore": 20
    },  # 钻
    {
        "minecraft:gold_ore": 20,
        "minecraft:gold_block": 1,
        "minecraft:cobblestone": 40,
        "minecraft:stone": 40,
        "minecraft:cobbled_deepslate": 40,
        "minecraft:deepslate_gold_ore": 30
    },  # 金
    {
        "minecraft:iron_ore": 20,
        "minecraft:iron_block": 1,
        "minecraft:cobblestone": 20,
        "minecraft:stone": 20,
        "minecraft:cobbled_deepslate": 20,
        "minecraft:deepslate_iron_ore": 30
    },  # 铁
    {
        "minecraft:lapis_ore": 20,
        "minecraft:lapis_block": 1,
        "minecraft:cobblestone": 40,
        "minecraft:stone": 40,
        "minecraft:cobbled_deepslate": 40,
        "minecraft:deepslate_lapis_ore": 25
    },  # 青金石
]

other_ore_blocks = {
    "minecraft:cobblestone": 500,
    "minecraft:stone": 600,
    "minecraft:cobbled_deepslate": 800,
    "minecraft:iron_ore": 120,
    "minecraft:gold_ore": 30,
    "minecraft:lapis_ore": 30,
    "minecraft:diamond_ore": 8,
    "minecraft:deepslate_iron_ore": 80,
    "minecraft:deepslate_gold_ore": 20,
    "minecraft:deepslate_lapis_ore": 20,
    "minecraft:deepslate_diamond_ore": 8
}

# 地狱岛方块生成权重
ore_block1 = {
    "minecraft:obsidian": 200,
    "minecraft:quartz_ore": 10,
}

dropped_item = ['minecraft:lapis_lazuli', 'minecraft:gold_ingot', 'minecraft:iron_ingot', 'minecraft:iron_block',
                'minecraft:gold_block', 'minecraft:lapis_block', 'minecraft:diamond', 'minecraft:diamond_block', 'minecraft:netherite_scrap', 'minecraft:netherite_ingot']
# 挖掘直接将掉落物给予玩家，方块 -> 物品 映射表
block_item_map = {
    "minecraft:gold_ore": "minecraft:gold_ingot",
    "minecraft:iron_ore": "minecraft:iron_ingot",
    "minecraft:diamond_ore": "minecraft:diamond",
    "minecraft:diamond_block": "minecraft:diamond_block",
    "minecraft:cobblestone": "minecraft:cobblestone",
    "minecraft:stone": "minecraft:cobblestone",
    "minecraft:cobbled_deepslate": "minecraft:cobbled_deepslate",
    "minecraft:deepslate_diamond_ore": "minecraft:diamond",
    "minecraft:deepslate_iron_ore": "minecraft:iron_ingot",
    "minecraft:gold_block": "minecraft:gold_block",
    "minecraft:deepslate_gold_ore": "minecraft:gold_ingot",
    "minecraft:iron_block": "minecraft:iron_block",
    "minecraft:lapis_block": "minecraft:lapis_block",
    "minecraft:deepslate_lapis_ore": "minecraft:lapis_lazuli",
    "minecraft:lapis_ore": "minecraft:lapis_lazuli",
    "minecraft:cherry_log": "minecraft:cherry_log",
    "minecraft:ancient_debris": "minecraft:netherite_scrap",
    "minecraft:quartz_ore": 'minecraft:quartz',
    "minecraft:obsidian": 'minecraft:obsidian'
}

exp_store = [
  {
    "item": 'minecraft:cobblestone',
    "aux": 0,
    "count": 64,
    "exp": 10
  },
  {
    "item": 'minecraft:cobbled_deepslate',
    "aux": 0,
    "count": 64,
    "exp": 15
  },
  {
    "item": 'minecraft:iron_ingot',
    "aux": 0,
    "count": 64,
    "exp": 100
  },
  {
    "item": 'minecraft:lapis_lazuli',
    "aux": 0,
    "count": 64,
    "exp": 200
  },
  {
    "item": 'minecraft:gold_ingot',
    "aux": 0,
    "count": 64,
    "exp": 200
  },
  {
    "item": 'minecraft:diamond',
    "aux": 0,
    "count": 64,
    "exp": 400
  },
  {
    "item": 'minecraft:emerald',
    "aux": 0,
    "count": 1,
    "exp": 100
  },
  {
    "item": 'minecraft:emerald_block',
    "aux": 0,
    "count": 1,
    "exp": 900
  },
  {
    "item": 'minecraft:quartz',
    "aux": 0,
    "count": 32,
    "exp": 100
  },
  {
    "item": 'minecraft:netherite_scrap',
    "aux": 0,
    "count": 1,
    "exp": 100
  },
]


item_store = [
  {
    "item": 'minecraft:iron_ingot',
    "aux": 0,
    "count": 32,
    "exp": 200
  },
  {
    "item": 'minecraft:lapis_lazuli',
    "aux": 0,
    "count": 32,
    "exp": 500
  },
  {
    "item": 'minecraft:gold_ingot',
    "aux": 0,
    "count": 32,
    "exp": 600
  },
  {
    "item": 'minecraft:diamond',
    "aux": 0,
    "count": 32,
    "exp": 800
  },
  {
    "item": 'minecraft:emerald',
    "aux": 0,
    "count": 1,
    "exp": 100
  },
  {
    "item": 'minecraft:emerald_block',
    "aux": 0,
    "count": 1,
    "exp": 900
  },
  {
    "item": 'minecraft:netherite_scrap',
    "aux": 0,
    "count": 1,
    "exp": 800
  },
  {
    "item": 'minecraft:netherite_upgrade_smithing_template',
    "aux": 0,
    "count": 1,
    "exp": 200
  },
  {
    "item": 'minecraft:bow',
    "aux": 0,
    "count": 1,
    "exp": 150
  },
  {
    "item": 'minecraft:crossbow',
    "aux": 0,
    "count": 1,
    "exp": 180
  },
  {
    "item": 'minecraft:arrow',
    "aux": 0,
    "count": 16,
    "exp": 128
  },
  {
    "item": 'minecraft:book',
    "aux": 0,
    "count": 16,
    "exp": 64
  },
  {
    "item": 'minecraft:apple',
    "aux": 0,
    "count": 16,
    "exp": 64
  },
  {
    "item": 'minecraft:snowball',
    "aux": 0,
    "count": 16,
    "exp": 32
  },
  {
    "item": 'zmqy:fire_ball',
    "aux": 0,
    "count": 1,
    "exp": 200
  },
  {
    "item": 'minecraft:potion',
    "aux": 33,
    "count": 1,
    "exp": 300
  }
]



# 地狱刷怪权重
spawn_mod_weight = {
    "minecraft:zombie_pigman": 50, # 僵尸猪人
    "minecraft:blaze": 10, # 烈焰人
    "minecraft:piglin": 30, # 僵尸猪灵
    "minecraft:piglin_brute": 10, # 猪灵蛮兵
    "minecraft:magma_cube": 20, # 岩浆怪
    "minecraft:zombie": 5, # 僵尸， 手持特殊物品（如力量药水）击杀掉落
    "minecraft:wither_skeleton": 20 #凋零骷髅
}

# 怪物击杀掉落特殊物品
