# -*- coding: utf-8 -*-

from common.mod import Mod
import server.extraServerApi as serverApi

@Mod.Binding(name='Bedwars_8_1_3Mod', version='1.0.0')
class Bedwars_8_1_3Server(object):
    
    @Mod.InitServer()
    def Bedwars_8_1_3ServerInit(self):
        serverApi.RegisterSystem('Bedwars_8_1_3Mod', 'Bedwars_8_1_3ServerSystem', 'Bedwars_8_1_3Script.Bedwars_8_1_3ServerSystem.Bedwars_8_1_3ServerSystem')

    @Mod.DestroyServer()
    def Bedwars_8_1_3Server<PERSON><PERSON>roy(self):
        pass
