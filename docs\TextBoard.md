[返回](../README.md)
# TextBoard
- 文字面板插件，封装客户端sfx操作
```python
TextBoardSys = serverApi.GetSystem('TextBoardMod', 'TextBoardServerSystem')
```
---
## 接口
### **newBoard**
- 创建一个文字面板

|参数|类型|说明|
|:---:|:---:|:---:|
|tag|str|面板的唯一标签|
|data|dict|数据信息|
|*dim|int|维度(-1代表全维度)|
|*player|str|仅渲染的玩家pid|

```python
#All attr & default
data = {
  'text': '',
  'pos': (0,0,0),
  'rot': (0,0,0),
  'size': (1.8,1.8),
  'face': True,
  'depth': False,
  'fontColor': (1,1,1,1),
  'tagColor': (0,0,0,0),
  'shadowColor': (0,0,0,1),
  'bind': None,
  'visible': True,
  'player': None,
  'tag': 'default',
  'dim': -1
}
TextBoardSys = serverApi.GetSystem('TextBoardMod', 'TextBoardServerSystem')
TextBoardSys.newBoard('testTag',{
  "text":"The text at position 0,4,0",
  "pos":(0,4,0)
})
```

### **setBoard**
- 设置一个文字面板，将data数据合并，若不存在将创建

|参数|类型|说明|
|:---:|:---:|:---:|
|tag|str|面板的唯一标签|
|data|dict|数据信息|
|*dim|int|维度(-1代表全维度)|
|*player|str|仅渲染的玩家pid|

```python
TextBoardSys = serverApi.GetSystem('TextBoardMod', 'TextBoardServerSystem')
TextBoardSys.setBoard('testTag',{
  "text":"The text change position to 6,6,6",
  "pos":(6,6,6)
})
```

### **delBoard**
- 删除某个文字面板

|参数|类型|说明|
|:---:|:---:|:---:|
|tag|str|面板的唯一标签||
|*dim|int|维度(-1代表全维度)|
|*player|str|仅渲染的玩家pid|

```python
TextBoardSys = serverApi.GetSystem('TextBoardMod', 'TextBoardServerSystem')
TextBoardSys.delBoard('testTag')
```

### **delDim**
- 删除某个维度的文字面板

|参数|类型|说明|
|:---:|:---:|:---:|
|dim|int|维度(-1代表全维度，-2代表全房间)|


```python
TextBoardSys = serverApi.GetSystem('TextBoardMod', 'TextBoardServerSystem')
TextBoardSys.delDim(-1)
```

### **delForOnePlayer**
- 删除某个玩家的专属面板

|参数|类型|说明|
|:---:|:---:|:---:|
|player|str|玩家pid(all代表全部玩家)|

```python
TextBoardSys = serverApi.GetSystem('TextBoardMod', 'TextBoardServerSystem')
TextBoardSys.delForOnePlayer(serverApi.GetPlayerList()[0])
```
### **delEntity**
- 删除绑定在某个实体上的所有面板

|参数|类型|说明|
|:---:|:---:|:---:|
|eid|str|生物entityId|

```python
TextBoardSys = serverApi.GetSystem('TextBoardMod', 'TextBoardServerSystem')
TextBoardSys.delEntity(serverApi.GetPlayerList()[0])
```
