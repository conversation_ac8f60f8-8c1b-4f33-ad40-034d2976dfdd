[返回](../README.md)
# AlertBoard
- 通用弹窗面板插件，封装客户端UI操作
- *本插件全部方法已集成至Player类中
----
## 接口
### **Alert**
- 向玩家发送使玩家打开一个新的弹窗面板

|参数|类型|说明|
|:---:|:---:|:---:|
|title|str|弹窗标题|
|text|str|弹窗内容|
|buttons|tuple(str,str)|按钮名字元祖;长度[1,3]|
|tag|str|弹窗唯一id|
|cb|func|回调函数，(player,tag,index)|

```python
def alertCallback(player,tag,index):
    player = toPlayer(player)
player.AlertBoard("请选择:","你确定要xxx吗？",tag='test',cb=alertCallback)
```

### **InputBoard**
- 向玩家发送使玩家打开一个新的弹窗输入面板

|参数|类型|说明|
|:---:|:---:|:---:|
|title|str|弹窗标题|
|text|str|弹窗输入内容|
|tag|str|输入弹窗唯一id|
|cb|func|回调函数，(player,tag,text,index)|

```python
def inputCallback(player,tag,text,index):
    player = toPlayer(player)
player.InputBoard("请输入:","输入玩家名字或uid进行查询",tag='test',cb=inputCallback)
```