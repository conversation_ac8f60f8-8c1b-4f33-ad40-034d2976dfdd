# -*- coding: utf-8 -*-
import copy
import uuid

from Item import Item
from Pos import Pos
from Entity import Entity
import mod.server.extraServerApi as serverApi
import lobbyGame.netgameApi as lobbyGameApi
_Server_System = serverApi.GetServerSystemCls()
_BCSys = _Server_System('common', 'game')


# player class

class Player(Entity):
    def __init__(self, playerId):
        super(Player, self).__init__(playerId)
        self.id = playerId

        # ModVariable
        self.safe = False
        self.isSpectator = False
        self.spectateBindId = None
        self.extra_name_dict = {}
        self.extra_nameTag_dict = {}
        self.player_name = self.getRealName()
        self.realTeamId = None
        
    # Get:
    def getPos(self):
        # type:() -> Pos
        pos = super(Player, self).getPos()
        return pos

    def setPos(self, pos):
        super(Player, self).setPos(pos)
        
    def getGamemode(self):
        comp = serverApi.GetEngineCompFactory().CreateGame(serverApi.GetLevelId())
        return comp.GetPlayerGameType(self.id)

    def setGamemode(self, gamemode):
        comp = serverApi.GetEngineCompFactory().CreatePlayer(self.id)
        return comp.SetPlayerGameType(gamemode)

    def getUid(self):
        return lobbyGameApi.GetPlayerUid(self.id)

    def GetPlatformUid(self):
        return lobbyGameApi.GetPlatformUid(self.id)

    def GetPlatForm(self):
        """PE: True  PC: False"""
        return lobbyGameApi.IsPlayerPeUser(self.id)

    def getName(self):
        return lobbyGameApi.GetPlayerNickname(self.id)
    
    def getItem(self, slotPos, posType=0):
        comp = serverApi.GetEngineCompFactory().CreateItem(self.id)
        return Item(comp.GetPlayerItem(posType, slotPos, True))

    def getExp(self, isPer=False):
        '''isPer: 是否返回百分比'''
        comp = serverApi.GetEngineCompFactory().CreateExp(self.id)
        return comp.GetPlayerExp(isPer)

    def getTotalExp(self):
        '''获取经验总值'''
        comp = serverApi.GetEngineCompFactory().CreateExp(self.id)
        return comp.GetPlayerTotalExp()

    def getLevel(self):
        comp = serverApi.GetEngineCompFactory().CreateLv(self.id)
        return comp.GetPlayerLevel()

    def getSpawnPoint(self):
        '''获取出生点 -> pos'''
        comp = serverApi.GetEngineCompFactory().CreatePlayer(self.id)
        x, y, z = comp.GetPlayerRespawnPos()['pos']
        dim = comp.GetPlayerRespawnPos()['dimensionId']
        return Pos(x, y, z, dim)

    def getNearPlayers(self, exceptId=None):
        '''获取玩家附近玩家(list<Player>:  要排除的玩家)
        默认不排除自己(None)
        '''
        exc = []
        if exceptId:
            for p in exceptId:
                exc.append(p.entityId)
            else:
                p = None
        comp = serverApi.GetEngineCompFactory().CreatePlayer(self.id)
        comp.GetRelevantPlayer(exc)

    def getFly(self):
        '''获取玩家飞行状态'''
        comp = serverApi.GetEngineCompFactory().CreateFly(self.id)
        return comp.IsPlayerFlying()

    def getSafe(self):
        """获取玩家的无敌状态(不触发被攻击事件)"""
        return self.safe
    
    # Set:
    def setMoveable(self, state):
        comp = serverApi.GetEngineCompFactory().CreatePlayer(self.id)
        comp.SetPlayerMovable(state)

    def setJumpable(self, state):
        comp = serverApi.GetEngineCompFactory().CreatePlayer(self.id)
        comp.SetPlayerJumpable(state)

    def setItem(self, item, slotPos=-1, posType=0):
        '''设置玩家物品(item:物品信息词典,slotPos: 槽位, posType: 槽位类型)'''
        itemDict = item.info
        comp = serverApi.GetEngineCompFactory().CreateItem(self.id)
        return comp.SetPlayerAllItems({
            (posType, slotPos): itemDict
        }).values()[0]

    def setTotalExp(self, exp):
        '''设置经验总量(exp: 经验总量)'''
        comp = serverApi.GetEngineCompFactory().CreateExp(self.id)
        return comp.SetPlayerTotalExp(exp)

    def setFly(self, state):
        '''设置飞行状态(state: bool)'''
        comp = serverApi.GetEngineCompFactory().CreateFly(self.id)
        return comp.ChangePlayerFlyState(state)

    def setMoveable(self, state):
        comp = serverApi.GetEngineCompFactory().CreatePlayer(self.id)
        comp.SetPlayerMovable(state)

    def setSpawnPoint(self, pos):
        comp = serverApi.GetEngineCompFactory().CreatePlayer(self.id)
        suc = comp.SetPlayerRespawnPos(pos.tup, pos.dim)

    def setSafe(self, isSafe):
        """设置玩家进入无敌状态(不触发被攻击事件)"""
        self.safe = isSafe

    def setExtraNameTag(self, name, layer=1):
        """
        设置玩家nametag前后缀
        :param name: 名字
        :param layer: 层级, 玩家本名为0级，不可设置layer==0；名字前缀层级为负数，名字后缀层级为正数，层级绝对值越小离玩家本名越近（类似数轴，玩家本名为原点）
        :return:
        """
        self.extra_nameTag_dict[layer] = name
        front = ''
        back = ''
        for i in sorted(self.extra_nameTag_dict.keys()):
            text = self.extra_nameTag_dict[i]
            if i < 0:
                front += text
            else:
                back += text
        comp = serverApi.GetEngineCompFactory().CreateName(self.id)
        comp.SetPlayerPrefixAndSuffixName(front, serverApi.GenerateColor('WHITE'), back, serverApi.GenerateColor('WHITE'), serverApi.GenerateColor('WHITE'))

    def setExtraName(self, name, layer=1):
        """
        设置玩家名字前后缀
        :param name: 名字
        :param layer: 层级, 玩家本名为0级，不可设置layer==0；名字前缀层级为负数，名字后缀层级为正数，层级绝对值越小离玩家本名越近（类似数轴，玩家本名为原点）
        :return:
        """
        self.extra_name_dict[layer] = name
        front = ''
        back = ''
        for i in sorted(self.extra_name_dict.keys()):
            text = self.extra_name_dict[i]
            if i < 0:
                front += text
            else:
                back += text
        self.player_name = front + self.getRealName() + back

    def getRealName(self):
        return lobbyGameApi.GetPlayerNickname(self.id)

    def getName(self):
        return copy.copy(self.player_name)

    # Action:
    def addExp(self, value):
        comp = serverApi.GetEngineCompFactory().CreateExp(self.id)
        comp.AddPlayerExperience(value)

    def addLevel(self, value):
        comp = serverApi.GetEngineCompFactory().CreateLv(self.id)
        comp.AddPlayerLevel(value)

    def pickUp(self, item):
        '''捡起掉落物(item: Entity)'''
        comp = serverApi.GetEngineCompFactory().CreateGame(self.id)
        comp.PickUpItemEntity(self.id, item.entityId)

    def breakBlock(self, pos, showPrtcl, sendInv):
        '''使用玩家手上物品破坏方块(pos: Pos, 是否展示粒子: int 0/1, 是否更新背包: bool)'''
        comp = serverApi.GetEngineCompFactory().CreateBlockInfo(
            self.id)  # 此处playerId为block的破坏者
        comp.PlayerDestoryBlock(pos.tup, showPrtcl, sendInv)

    def cmd(self, cmd, showOutput=False):
        comp = serverApi.GetEngineCompFactory().CreateCommand(serverApi.GetLevelId())
        comp.SetCommand(cmd, self.id, showOutput)

    def _title(self, title, subTitle=None):
        '''调用原版title(title: 显示内容, subTitle: 是否为副标题)'''
        self.cmd('/title @s title '+title)
        if subTitle:
            self.cmd('/title @s subtitle '+subTitle)

    def actionbar(self, text):
        '''调用原版actionbar(text: 显示内容)'''
        self.cmd('/title @s actionbar '+text)

    def popup(self, title, text):
        '''显示popup(title: 标题, text: 文字)'''
        comp = serverApi.GetEngineCompFactory().CreateGame(self.id)
        comp.SetOnePopupNotice(self.id, str(title), str(text))

    def tip(self, text):
        comp = serverApi.GetEngineCompFactory().CreateGame(self.id)
        comp.SetOneTipMessage(self.id, str(text))

    def say(self, text):
        '''向玩家聊天栏发送消息(text: 内容)'''
        comp = serverApi.GetEngineCompFactory().CreateMsg(self.id)
        comp.NotifyOneMessage(self.id, str(text))

    def kill(self):
        '''击杀玩家'''
        self.cmd('/kill', False)

    def NotifyToClient(self, eventStr, data):
        '''向玩家客户端发送事件'''
        _BCSys.NotifyToClient(self.id, eventStr, data)

    #API
    def title(self,text):
        # type:(str) -> None
        pass
    
    def subTitle(self,text):
        # type:(str) -> None
        pass
    
    def upbar(self,text,color=(0,0,0,0.5),index=2):
        # type:(str,tuple,int) -> None
        pass
    
    def notice(self,text,render=None,color=(0,0,0,0.5)):
        # type:(str,dict,tuple) -> None
        pass

    def setRightText(self, text):
        # type:(str) -> None
        pass


    class __ScoreBoard(object):
        def __init__(self, pid, cls):pass
        def addLine(self, mode='right', line=0): return self
        def addText(self, text='', index=-1, color=(0,0,0,0.5), line=0):return self
        def setLineMode(self, mode, line=0):return self
        def delLine(self, line):return self
        def resetLine(self, line):return self
        def setText(self, text, index, line=0):return self
        def delText(self, index, line=0):return self
        def setTextIndex(self, old_index, new_index, line=0):return self
        def setTextColor(self, color=(0,0,0,0.5), index=0, line=0):return self
        def setData(self, data):return self
        def send(self):return self

    ScoreBoard = __ScoreBoard(0,0)

    class __ButtonBoard(object):  
        def newRender(self,type,content,num='',aux=0,enchant=False):return dict()
        def newPage(self,pageId=None,callback=None):return
        def setPage(self,pageId,content='',buttonList=[],title=None,subtitle=None,backId=None,cache=True):return
        def changePage(self,pageName,pageId):return
        def newButton(self,text=None,renderL=None,renderR=None,textFormat=[],waitRes=False,buttonId=None,callback=None,args=None):return dict()
        def setButton(self,buttonId_or_button,text=None,renderL=None,renderR=None,textFormat=[],waitRes=False,callback=None,args=None):return
        def setLoad(self,isLoad):return
        def alert(self,text):return
        def send(self,pages,initPages={},select=None):return
        def refetch(self,refetch=None):return
        def setText(self,title=None,subtitle=None,content=None):return
        def close(self):return
        
    bb = __ButtonBoard()
    
    def newButtonBoard(self,title=None,subtitle=None):return self.bb
        
    def AlertBoard(self,title='请选择: ',text='undefined',buttons=('§c取消','§a确定'),tag='undefined',cb=None):return
       
    def InputBoard(self,title='请输入: ',text='请输入文字:',tag='undefined',cb=None):return
    
    class ServerForm(object): # 表单构建器，封装表单List
        def __init__(self, title='未命名表单'):
            self.data = [] # 表单元数据
            self.title = title # 表单的标题
            self.boardId = str(uuid.uuid4()) # 表单的唯一标识
        def label(self, text=''): return self
        def image(self, path='textures/ui/rating_screen', size=(260, 64)):return self
        def button(self, text='未命名按钮', tag='undefined'): return self
        def imgButton(self, text='未命名按钮', path='textures/ui/redX1', tag='undefined'): return self
        def itemButton(self, text='未命名按钮', item_name='minecraft:unknown', item_aux=0, item_ench=False, tag='undefined'): return self
        def inputBox(self, text='请输入文字:', holder='请输入内容', default='', tag='undefined'): return self
        def toggle(self, text='开/关', default=False, tag='undefined'): return self
        def slider(self, text='当前值: {value}%%', start=0, end=100, step=1, default=0, tag='undefined'): return self
        def paperDoll(self, entityId='', scale=1): return self
        def progressBar(self, value=0.8, color=(0, 0.6, 1)): return self
        def send(self, callback=None, player=None): return self

    def newServerForm(self, title='表单'):
        # type:(str) -> ServerForm
        return serverApi.GetSystem('ServerFormMod', 'ServerFormServerSystem').getFormBuilder()(title, self)

    def addMenuItem(self,item,text,tag,slot):
        # type:(Item,str,str,int) -> None
        pass

    def removeMenu(self):
        pass
    
    def setSpectator(self,bindId=None):
        # type:(str) -> None
        pass
    
    def quitSpectator(self):
        pass

    def SaveItemToDatabase(self, tag):
        # type:(str) -> None
        return

    def LoadItemFromDatabase(self, tag):
        # type:(str) -> None
        return

    def addTextBoard(self, text, pos, scale=1.2, color=(0, 0, 0, 0.3), deepTest=False, tag=None):
        pass

    def setTextBoard(self, tag, mode, value):
        pass

    def delTextBoard(self, tag):
        pass

    def kickSelf(self,reason,super_kick=False):
        """强力踢出可能无法显示踢出原因"""
        lobbyGameApi.TryToKickoutPlayer(self.id, reason)
        def super_kick_t():
            _Server_System = serverApi.GetServerSystemCls()
            _BCSys = _Server_System('common', 'game')
            _BCSys.NotifyToMaster('KickPlayer', {
                'uid': self.getUid(),
                'reason': reason
            })
        if super_kick:
            super_kick_t()
            return
        comp = serverApi.GetEngineCompFactory().CreateGame(serverApi.GetLevelId())
        comp.AddTimer(5,super_kick_t)