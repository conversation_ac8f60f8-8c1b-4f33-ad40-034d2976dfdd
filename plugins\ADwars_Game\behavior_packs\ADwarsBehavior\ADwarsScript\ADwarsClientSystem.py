# -*- coding: utf-8 -*-

import mod.client.extraClientApi as clientApi
from ClientApi import *

NativeScreenManager = clientApi.GetNativeScreenManagerCls()
ClientSystem = clientApi.GetClientSystemCls()

class ADwarsClientSystem(ClientSystem):
    def __init__(self, namespace, systemName):
        ClientSystem.__init__(self, namespace, systemName)
        
        Listener.ListenForEvent(self)
        self.mPlayerId = clientApi.GetLocalPlayerId()
        self.mLevelId = clientApi.GetLevelId()
        
    @Listener.Engine(Event.UiInitFinished)
    def UiInitFinished(self, args):
        # clientApi.RegisterUI('ADwarsMod', 'uiName', "ADwarsScript.ADwarsUi.ADwarsScreen", "uiName.main")
        # clientApi.CreateUI('ADwarsMod', 'uiName', {"isHud": 1},{'data':{},'client':self})
        # clientApi.PushScreen('ADwarsMod', 'uiName', {'data':{},'client':self})
        # NativeScreenManager.instance().RegisterScreenProxy("pause.pause_screen", "ADwarsScript.ADwarsUi.ADwarsProxy")
        pass
    
    @Listener.Engine(Event.ClientPlayerInventoryOpenEvent)
    def ClientPlayerInventoryOpenEvent(self, args):
        args['cancel'] = True
        self.NotifyToServer('ClientPlayerInventoryOpenEvent', {})
    
    @Listener.Engine(Event.LoadClientAddonScriptsAfter)
    def LoadClientAddonScriptsAfter(self, args):
        comp = clientApi.GetEngineCompFactory().CreateConfigClient(clientApi.GetLevelId())
        configDict = comp.GetConfigData("zmqy_adwars_item_position_config", True)
        print('configDict', configDict)
        if not configDict or configDict == {}:
            configDict = {
                0: 0,
                1: 1,
                2: 2,
                3: 3,
                4: 4,
                5: 5,
                6: 6,
                7: 7,
                8: 8
            }
            comp.SetConfigData("zmqy_adwars_item_position_config", configDict, True)
        self.NotifyToServer("PlayerUpdateItemPositionEvent",{'configDict':configDict})
        
        

    @Listener.Engine(Event.OnScriptTickClient)
    def OnScriptTickClient(self):
        pass
    
    @Listener.Server('StorageSettingsEvent')
    def StorageSettingsEvent(self, args):
        print('StorageSettingsEvent', args)
        comp = clientApi.GetEngineCompFactory().CreateConfigClient(clientApi.GetLevelId())
        comp.SetConfigData("zmqy_adwars_item_position_config", args['configDict'], True)

    def Destroy(self):
        Listener.UnListenForEvent(self)