<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="01ba3574-4dab-467b-b9ab-d2e01f1ec0c2" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/plugins/Common_Mod/behavior_packs/CommonBehavior/CommonScript/CommonClientSystem.py" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/Common_Mod/behavior_packs/CommonBehavior/CommonScript/CommonClientSystem.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2yrHv5CTSmxZ7mjwK6K1taFEcB6" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python.CreateMod.executor&quot;: &quot;Run&quot;,
    &quot;Python.新建 文本文档 (2).executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Python.CreateMod">
    <configuration name="新建 文本文档 (2)" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="ZMQY" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/../.." />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/../../新建 文本文档 (2).py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="CreateMod" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="ZMQY" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/tools/CreateMod" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/tools/CreateMod/CreateMod.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.CreateMod" />
        <item itemvalue="Python.新建 文本文档 (2)" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-PY-251.26094.141" />
        <option value="bundled-python-sdk-9f8e2b94138c-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26094.141" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="01ba3574-4dab-467b-b9ab-d2e01f1ec0c2" name="更改" comment="" />
      <created>1750586000767</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750586000767</updated>
      <workItem from="1750586003037" duration="224000" />
      <workItem from="1750598680979" duration="1072000" />
      <workItem from="1750636669255" duration="1994000" />
      <workItem from="1750662863528" duration="21000" />
      <workItem from="1750662901540" duration="327000" />
      <workItem from="1750683698949" duration="34000" />
      <workItem from="1750723779063" duration="564000" />
      <workItem from="1750725112474" duration="7799000" />
      <workItem from="1750742413487" duration="651000" />
      <workItem from="1750744246745" duration="1023000" />
      <workItem from="1750750276154" duration="526000" />
      <workItem from="1750750921710" duration="555000" />
      <workItem from="1750753388620" duration="2150000" />
      <workItem from="1750900312913" duration="775000" />
      <workItem from="1750902848934" duration="907000" />
      <workItem from="1750903870427" duration="2887000" />
      <workItem from="1750909933933" duration="1439000" />
      <workItem from="1750911912229" duration="401000" />
      <workItem from="1750914682797" duration="2076000" />
      <workItem from="1750919674308" duration="966000" />
      <workItem from="1750921645059" duration="256000" />
      <workItem from="1750921997529" duration="2071000" />
      <workItem from="1750924242495" duration="15000" />
      <workItem from="1750925859522" duration="493000" />
      <workItem from="1750926387389" duration="49000" />
      <workItem from="1750927285693" duration="194000" />
      <workItem from="1751712136859" duration="13000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/AntiCheatingServerSystem_py$___2_.coverage" NAME="新建 文本文档 (2) 覆盖结果" MODIFIED="1750730350618" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../.." />
    <SUITE FILE_PATH="coverage/CommonServiceSystem_py$CreateMod.coverage" NAME="CreateMod 覆盖结果" MODIFIED="1750903743073" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tools/CreateMod" />
    <SUITE FILE_PATH="coverage/CreateMod_py$CreateMod.coverage" NAME="CreateMod 覆盖结果" MODIFIED="1750724022488" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tools/CreateMod" />
  </component>
</project>