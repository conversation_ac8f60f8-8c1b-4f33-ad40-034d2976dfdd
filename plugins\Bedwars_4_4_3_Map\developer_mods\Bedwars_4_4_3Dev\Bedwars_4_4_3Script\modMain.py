# -*- coding: utf-8 -*-

from common.mod import Mod
import server.extraServerApi as serverApi

@Mod.Binding(name='Bedwars_4_4_3Mod', version='1.0.0')
class Bedwars_4_4_3Server(object):
    
    @Mod.InitServer()
    def Bedwars_4_4_3ServerInit(self):
        serverApi.RegisterSystem('Bedwars_4_4_3Mod', 'Bedwars_4_4_3ServerSystem', 'Bedwars_4_4_3Script.Bedwars_4_4_3ServerSystem.Bedwars_4_4_3ServerSystem')

    @Mod.DestroyServer()
    def Bedwars_4_4_3Server<PERSON><PERSON>roy(self):
        pass
