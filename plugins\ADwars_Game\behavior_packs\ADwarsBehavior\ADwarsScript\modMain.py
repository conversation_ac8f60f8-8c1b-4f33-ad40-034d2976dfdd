# -*- coding: utf-8 -*-

from common.mod import Mod
import client.extraClientApi as clientApi
from mod_log import logger as log

@Mod.Binding(name="ADwarsMod", version="1.0.0")
class ADwarsClient(object):
	@Mod.InitClient()
	def ADwarsClientInit(self):
		clientApi.RegisterSystem("ADwarsMod", "ADwarsClientSystem", "ADwarsScript.ADwarsClientSystem.ADwarsClientSystem")
		print('=====> ADwars Init <=====')
	@Mod.DestroyClient()
	def ADwarsClientDestroy(self):
		print('=====> ADwars Destroy <=====')
