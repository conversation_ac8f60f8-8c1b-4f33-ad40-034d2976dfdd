# -*- coding: utf-8 -*-
import time

import lobbyGame.netgameApi as netgameApi
import apolloCommon.mysqlPool as mysqlPool
import mod.server.extraServerApi as serverApi
import lobbyGame.netgameConsts as netgameConsts
from api.api import *
from ADwarsGame import ADwarsGame
from mod_log import logger
import item

ServerSystem = serverApi.GetServerSystemCls()


class ADwarsServerSystem(ServerSystem):
    def __init__(self, namespace, systemName):
        ServerSystem.__init__(self, namespace, systemName)
        print('=====> ADwars init <=====')
        Listener.ListenForEvent(self)
        self.mlevelId = serverApi.GetLevelId()
        self.mserverId = lobbyGameApi.GetServerId()
        self.battleGrounds = {}  # dim -> GameClass
        self.playerDim = {}  # playerId -> dim
        self.playerBornDimTmp = {}  # uid -> {'dim':dim,'time':time.time()}
        self.loaded_players = set()
        self.game_over_dims = set()
        self.map = None
        self.gameCfg = {}
        self.mapCfg = {}
        self.first_player_loaded = False
        self.setWorldConfig()
        Timer.repeat(1, self.GameTickEvent)

    @Listener.Engine(Event.LoadServerAddonScriptsAfter)
    def LoadServerAddonScriptsAfter(self, args):
        pass

    def GameOver(self, dim):
        self.game_over_dims.add(dim)
        del self.battleGrounds[dim] # 释放游戏类
        if len(self.game_over_dims) >= len(self.mapCfg['dimension_list']): # 本房间全部维度用完
            lobbyGameApi.ShutdownServer()

    @Listener.Custom('RoomServiceConnectEvent')
    def RegisterRoom(self, args):
        CommonConfig = lobbyGameApi.GetCommonConfig()
        for server in CommonConfig['serverlist']:
            if server['serverid'] == self.mserverId:
                mods = server['mods'].split(',')
                for mod in mods:
                    if '_Map' in mod:  # 是地图
                        self.map = mod
                        break
                break
        self.gameCfg = api.uniToUtf(commonNetgameApi.GetModJsonConfig('ADwarsScript'))['data']
        self.mapCfg = api.uniToUtf(commonNetgameApi.GetModJsonConfig(self.map[:-4] + 'Script'))['data']
        battle_type = self.mapCfg['battle_type']
        dimension_list = self.mapCfg['dimension_list']
        max_player = self.mapCfg['max_player']
        max_member = self.mapCfg['max_member']
        sys = serverApi.GetSystem('RoomMod', 'RoomServerSystem')

        def execute():
            def cb(data):  # 注册房间
                if not data['suc']:
                    logger.error(data['msg'])
                    Timer.delay(5, execute)

            sys.RegisterGameRoom(battle_type, dimension_list, max_player, max_member, cb)

        execute()
        for dim in dimension_list:  # 复制维度，并设置常加载以防玩家退出重置维度
            comp = serverApi.GetEngineCompFactory().CreateDimension(self.mlevelId)
            print 'MirrorDimension {}:  {}'.format(dim, comp.MirrorDimension(0, dim))
            comp = serverApi.GetEngineCompFactory().CreateChunkSource(self.mlevelId)
            comp.SetAddArea('GameBase' + str(dim), dim, (-80, 0, -80), (80, 80, 80))
            # 实例化维度战场
            self.battleGrounds[dim] = ADwarsGame(dim, self.gameCfg, self.mapCfg, self)

    @Listener.Custom('TransferToGameGetDimEvent', 'service')
    def TransferToGameGetDimEvent(self, args):
        dim = args['dim']
        uid = args['uid']
        teamId = args['teamId']
        # print args
        extra_info = args['extra_info']
        for game in self.battleGrounds.values():
            game.extra_info = extra_info
        self.playerBornDimTmp[uid] = {
            'dim': dim,
            'time': time.time(),
            'teamId': teamId
        }

    @Listener.Engine('OnScriptTickServer')
    def OnScriptTickServer(self):
        for bg in self.battleGrounds.values():
            bg.OnScriptTickServer()

    def GameTickEvent(self):
        for bg in self.battleGrounds.values():
            try:
                bg.GameTickEvent()
            except Exception as e:
                print e
                pass
        now = time.time()
        for uid in self.playerBornDimTmp.copy():
            if now - self.playerBornDimTmp[uid]['time'] > 45:
                dim = self.playerBornDimTmp[uid]['dim']
                try:
                    if Pl(lobbyGameApi.GetPlayerIdByUid(uid)) not in self.loaded_players:
                        Pl(lobbyGameApi.GetPlayerIdByUid(uid)).kickSelf('客户端模组加载超时')
                except:
                    pass
                if not len(self.battleGrounds[dim].players):
                    self.battleGrounds[dim].ToFreeRoom()
                del self.playerBornDimTmp[uid]

    @Listener.Engine('ServerPlayerBornPosEvent')
    def ServerPlayerBornPosEvent(self, args):
        args['retRespawn'] = True
        args['ret'] = True
        uid = args['userId']
        dim = None
        if uid in self.playerBornDimTmp:
            dim = self.playerBornDimTmp[uid]['dim']
            args['dimensionId'] = dim
            self.playerDim[lobbyGameApi.GetPlayerIdByUid(uid)] = dim
        else:
            _Server_System = serverApi.GetServerSystemCls()
            _BCSys = _Server_System('common', 'game')
            _BCSys.NotifyToMaster('KickPlayer', {
                'uid': uid,
                'reason': '未经分房系统进入游戏房间'
            })
            return
        self.battleGrounds[dim].PlayerBornEvent(
            uid,
            args
        )

    @Listener.Custom(Event.AddPlayerCommonEvent)
    def AddPlayerCommonEvent(self, args):
        player = args['player']  # type: Player
        dim = player.getPos().dim
        self.battleGrounds[dim].PlayerCreateEvent(player)

    @Listener.Custom(Event.ClientLoadedCommonEvent)
    def ClientLoadedCommonEvent(self, args):
        player = args['player']  # type: Player
        dim = player.getPos().dim
        player.teamId = self.playerBornDimTmp[player.getUid()]['teamId']
        self.loaded_players.add(player)
        self.battleGrounds[dim].PlayerLoadedEvent(player)
        if not self.first_player_loaded:
            self.first_player_loaded = True
            api.cmd('/gamerule falldamage false')
            api.cmd('/gamerule showtags false')
            api.cmd('/gamerule showcoordinates true')
            api.cmd('/gamerule commandblockoutput false')
            api.cmd('/gamerule sendcommandfeedback false')

    @Listener.Engine(Event.PlayerIntendLeaveServerEvent)
    def PlayerIntendLeaveServerEvent(self, args):
        player = Pl(args['playerId'])  # type: Player
        dim = player.getPos().dim
        self.loaded_players.discard(player)
        self.battleGrounds[dim].PlayerLeaveEvent(player)

    @Listener.Engine('PlayerAttackEntityEvent')
    def PlayerAttackEntityEvent(self, args):
        player = Pl(args['playerId'])
        target = Pl(args['victimId']) if args['victimId'] in GetPlayerDict() else En(args['victimId'])
        dim = player.getPos().dim
        self.battleGrounds[dim].PlayerAttackEvent(
            player,
            target,
            args
        )

    @Listener.Custom('PlayerHurtCommonEvent')
    def PlayerHurtCommonEvent(self, args):
        player = args['player']  # type: Player
        dim = player.getPos().dim
        self.battleGrounds[dim].PlayerHurtEvent(
            args['player'],
            args['attacker'],
            args['damage'],
            args['cause'],
            args['projectile'],
            args['args']
        )

    @Listener.Custom('PlayerDeathCommonEvent')
    def PlayerDeathEvent(self, args):
        player = args['player']  # type: Player
        dim = player.getPos().dim
        self.battleGrounds[dim].PlayerDeathEvent(
            args['player'],
            args['killer'],
            args['cause'],
            args['attackers'],
            args['projectile'],
            args
        )

    @Listener.Engine('ProjectileDoHitEffectEvent')
    def ProjectileDoHitEffectEvent(self, args):
        bullet = En(args['id'])
        dim = bullet.getPos().dim
        self.battleGrounds[dim].PlayerShootEvent(
            Pl(args['srcId']) if args['srcId'] else None,
            bullet,
            args['hitTargetType'] == 'ENTITY',
            Pos(args['x'], args['y'], args['z'], dim) if args['hitTargetType'] == 'ENTITY' else Pos(
                args['blockPosX'], args['blockPosY'], args['blockPosZ'], dim),
            Pl(args['targetId']) if args['targetId'] in GetPlayerDict() else None,
            args['hitFace'],
            args
        )

    @Listener.Engine('ServerPlayerTryDestroyBlockEvent')
    def ServerPlayerTryDestroyBlockEvent(self, args):
        self.battleGrounds[args['dimensionId']].TryDestroyBlockEvent(
            Pl(args['playerId']),
            Pos(args['x'], args['y'], args['z'], args['dimensionId']),
            args['fullName'],
            args['auxData'],
            args['face'],
            args,
            args['spawnResources']
        )

    @Listener.Engine('DestroyBlockEvent')
    def DestroyBlockEvent(self, args):
        self.battleGrounds[args['dimensionId']].DestroyBlockEvent(
            Pl(args['playerId']),
            Pos(args['x'], args['y'], args['z'], args['dimensionId']),
            args['fullName'],
            args['auxData'],
            args['face'],
        )

    @Listener.Engine('ServerEntityTryPlaceBlockEvent')
    def ServerEntityTryPlaceBlockEvent(self, args):
        if args['entityId'] not in GetPlayerDict():
            return
        self.battleGrounds[args['dimensionId']].TryPlaceBlockEvent(
            Pl(args['entityId']),
            Pos(args['x'], args['y'], args['z'], args['dimensionId']),
            args['fullName'],
            args['auxData'],
            args['face'],
            args,
        )
        if args['cancel']:
            Timer.delay(0, lambda: item.force_sync_player_inventory(args['entityId']))

    @Listener.Engine('EntityPlaceBlockAfterServerEvent')
    def EntityPlaceBlockAfterServerEvent(self, args):
        if args['entityId'] not in GetPlayerDict():
            return
        self.battleGrounds[args['dimensionId']].PlaceBlockEvent(
            Pl(args['entityId']),
            Pos(args['x'], args['y'], args['z'], args['dimensionId']),
            args['fullName'],
            args['auxData'],
            args['face'],
        )

    @Listener.Engine('ServerPlayerTryTouchEvent')
    def ServerPlayerTryTouchEvent(self, args):
        player = Pl(args['playerId'])
        self.battleGrounds[player.getPos().dim].TryPickItemEvent(
            player,
            En(args['entityId']),
            Item(args['itemDict']),
            args,
        )

    @Listener.Engine('ServerItemTryUseEvent')
    def ServerItemTryUseEvent(self, args):
        player = Pl(args['playerId'])
        self.battleGrounds[player.getPos().dim].TryUseItemEvent(
            Pl(args['playerId']),
            Item(args['itemDict']),
            args,
        )

    @Listener.Engine('ExplosionServerEvent')
    def ExplosionServerEvent(self, args):
        self.battleGrounds[args['dimensionId']].ExplosionEvent(
            args['blocks'],
            Pos(args['explodePos'][0], args['explodePos'][1], args['explodePos'][2]),
            args['victims'],
            args['sourceId'],
        )
    
    @Listener.Custom(Event.PlayerChatEvent)
    def PlayerChatEvent(self, args):
        player = args['player']
        msg = args['msg']
        self.battleGrounds[player.getPos().dim].PlayerChatEvent(
            player,
            msg,
            args['args']
        )
        if msg == "*mm":
            player.money += 1000
    
    @Listener.Custom('HotbarMenuDoubleClick')
    def HotbarMenuDoubleClick(self, args):
        player = Pl(args['playerId'])
        dim = player.getPos().dim
        self.battleGrounds[dim].HotbarMenuDoubleClickEvent(player, args['tag'], args['item'])

    @Listener.Client('ClientPlayerInventoryOpenEvent')
    def ClientPlayerInventoryOpenEvent(self, args):
        player = Pl(args['__id__'])
        dim = player.getPos().dim
        self.battleGrounds[dim].ClientPlayerInventoryOpenEvent(player)
    
    @Listener.Client('PlayerUpdateItemPositionEvent')
    def PlayerUpdateItemPositionEvent(self, args):
        player = Pl(args['__id__'])
        dim = player.getPos().dim
        self.battleGrounds[dim].PlayerUpdateItemPositionEvent(player, args['configDict'])

    def setWorldConfig(self):
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.ChunkLoadUsePriority, True)  # 加载额外地图
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.RedstoneOnTick, True)  # 屏蔽红石
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.ChunkSaveOnTick, True)  # 是否存档
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.WalkAnimPostEvent, True)  # 行走动画（可关闭）
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.RecipesSyncOnLogin, True)  # 合成，炼药，烧制
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.UpdateGlidingOnTick, True)  # 鞘翅滑翔
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.UpdateContainerOnTick, True)  # 熔炉，高炉，炼药台更新
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.PushEntitiesOnTick, True)  # 玩家推挤生物
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.UpdateInsideBlockOnTick, True)  # 传送门，仙人掌对玩家作用
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.BlockDamageOnTick, True)  # 岩浆块，营火伤害
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.SendDirtyActorPerTick, False)  # 帧检测降至1s（延迟同步属性）
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.ApplyExhaustionOnTick, True)  # 饥饿属性
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.UpdateInteractionOnTick, True)  # 生物交互窗口
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.UpdateOffhandItemOnTick, True)  # 不更新副手地图
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.PickEntityOnTick, False)  # 是否可捡起物品
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.SyncComplexItemOnTick, True)  # 不同步地图
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.SpawnMobsOnTick, True)  # 生成怪物/部分生物
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.UpdateBlocksOnTick, True)  # 不更新雨雪雷
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.UpdateWeatherOnTick, True)  # 不更新季节与天气
        lobbyGameApi.ChangePerformanceSwitch(netgameConsts.DisableSwitch.LoadSavedEntityFromChunk, True)  # 不加载生物与保存生物
        comp = serverApi.GetEngineCompFactory().CreateGame(self.mlevelId)
        comp.SetCanActorSetOnFireByLightning(False)  # 闪电出火
        comp.SetGameDifficulty(3)  # 游戏难度
        gameRuleDict = {
            'option_info': {
                'pvp': True,  # 玩家伤害
                'show_coordinates': True,  # 显示坐标
                'fire_spreads': False,  # 火焰蔓延
                'tnt_explodes': True,  # tnt爆炸
                'mob_loot': False,  # 生物战利品
                'natural_regeneration': False,  # 自然生命恢复
                'tile_drops': True,  # 方块掉落
                'immediate_respawn': True  # 立即重生
            },
            'cheat_info': {
                'enable': True,  # 是否开启作弊(需要在下方也进行配置)
                'always_day': True,  # 终为白日
                'mob_griefing': True,  # 生物破坏方块
                'keep_inventory': True,  # 保留物品栏
                'weather_cycle': False,  # 天气更替
                'mob_spawn': False,  # 生物生成
                'entities_drop_loot': False,  # 实体掉落
                'daylight_cycle': False,  # 开启昼夜交替
                'command_blocks_enabled': False,  # 启用方块命令
                'random_tick_speed': 0,  # 随机方块tick速度
            }
        }
        comp.SetGameRulesInfoServer(gameRuleDict)  # 游戏设置(上方字典配置)
        serverApi.GetEngineCompFactory().CreateGame(self.mlevelId).SetMergeSpawnItemRadius(2.5)  # 合并生成物品半径
        comp = serverApi.GetEngineCompFactory().CreateGame(self.mlevelId)
        comp.SetDefaultGameType(2)  # 默认冒险

    def Destroy(self):
        print('=====> ADwars Destroy <=====')
