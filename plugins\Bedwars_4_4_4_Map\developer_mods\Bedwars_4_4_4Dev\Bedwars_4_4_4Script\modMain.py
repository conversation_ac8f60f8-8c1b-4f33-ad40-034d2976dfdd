# -*- coding: utf-8 -*-

from common.mod import Mod
import server.extraServerApi as serverApi

@Mod.Binding(name='Bedwars_4_4_4Mod', version='1.0.0')
class Bedwars_4_4_4Server(object):
    
    @Mod.InitServer()
    def Bedwars_4_4_4ServerInit(self):
        serverApi.RegisterSystem('Bedwars_4_4_4Mod', 'Bedwars_4_4_4ServerSystem', 'Bedwars_4_4_4Script.Bedwars_4_4_4ServerSystem.Bedwars_4_4_4ServerSystem')

    @Mod.DestroyServer()
    def Bedwars_4_4_4Server<PERSON><PERSON>roy(self):
        pass
