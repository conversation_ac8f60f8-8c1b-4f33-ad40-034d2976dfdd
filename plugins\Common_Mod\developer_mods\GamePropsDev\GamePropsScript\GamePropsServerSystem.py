# -*- coding: utf-8 -*-
import random
import lobbyGame.netgameApi as netgameApi
import apolloCommon.mysqlPool as mysqlPool
import mod.server.extraServerApi as serverApi
from mod_log import logger
from api.api import *

ServerSystem = serverApi.GetServerSystemCls()

PropsList = ['zmqy:nade', 'zmqy:smoke', 'zmqy:flash', 'zmqy:shoulei']

class GamePropsServerSystem(ServerSystem):
    def __init__(self, namespace, systemName):
        ServerSystem.__init__(self, namespace, systemName)
        print('=====> GameProps init <=====')
        Listener.ListenForEvent(self)
        self.mlevelId = serverApi.GetLevelId()
        self.mserverId = lobbyGameApi.GetServerId()
        self.destroyingEntities = set()
        self.fireAreas = []
        Timer.repeat(0.48, self.tick480)
        Timer.repeat(1, self.tick1000)

    @Listener.Engine(Event.LoadServerAddonScriptsAfter)
    def ServerLoaded(self, args):
        pass

    @Listener.Engine(Event.PlayerEatFoodServerEvent)
    def PlayerEatFoodServerEvent(self, args):
        if args['itemDict']['newItemName'] == "zmqy:zhugeliannu":
            print("zmqy:zhugeliannu")
            comp = serverApi.GetEngineCompFactory().CreateProjectile(serverApi.GetLevelId())
            comp.CreateProjectileEntity(args['playerId'], "zmqy:arrow", None)


            
    @Listener.Engine('PlayerPickupArrowServerEvent')
    def PlayerPickupArrowServerEvent(self, args):
        entity = En(args['arrowId'])
        if entity.getType() in PropsList:
            args['cancel'] = True
            args['pickupDelay'] = 97813 # 永远不可拾取

    def tick480(self):
        # 处理火焰区域
        current_time = time.time()
        for area in self.fireAreas:
            if area['stopTime'] <= current_time:
                self.fireAreas.remove(area)
                continue
            comp = serverApi.GetEngineCompFactory().CreateGame(self.mserverId)
            areaEntityList = comp.GetEntitiesInSquareArea(None, tuple(area['area'][:3]), tuple(area['area'][-3:]), area['dim'])
            print('areaEntityList', areaEntityList)
            for entityId in areaEntityList:
                if entityId in GetPlayerDict():
                    player = Pl(entityId)
                    if player.id == area['source']: 
                        player.hurt(3, 'fire')
                    else:
                        player.hurt(3, 'fire', area['source'])
                    player.setOnFire(3)
    
    def tick1000(self):
        current_time = time.time()
        for area in self.fireAreas:
            if area['stopTime'] - current_time < 0.6:
                continue
            src = Pl(area['source'])
            for x in range(area['area'][0], area['area'][3] + 1):
                for z in range(area['area'][2], area['area'][5] + 1):
                    src.cmd('/particle minecraft:mobflame_single {} {} {}'.format(x + random.random() - 0.5, area['particleY'] + random.random() - 0.5, z + random.random() - 0.5))

    @Listener.Engine(Event.OnScriptTickServer)
    def OnScriptTickServer(self):
        Timer.Tick()
        



    @Listener.Engine(Event.ProjectileDoHitEffectEvent)
    def ProjectileDoHitEffectEvent(self, args):
        if args['srcId'] not in GetPlayerDict() or args['id'] in self.destroyingEntities:
            return
        origin_projectile_entity = En(args['id'])
        src = Pl(args['srcId'])
        entityType = origin_projectile_entity.getType()
        if entityType not in PropsList:
            return
        dim = origin_projectile_entity.getPos().dim
        entity = En(self.CreateEngineEntityByTypeStr(entityType, (args['x'], args['y'], args['z']), (0, 0), dim))
        self.destroyingEntities.add(origin_projectile_entity.id)
        def nextTick():
            self.DestroyEntity(origin_projectile_entity.id)
            self.destroyingEntities.discard(origin_projectile_entity.id)
        Timer.delay(0, nextTick)
        def func(new_args):
            pos = new_args['entity'].getPos()
            new_args['src'].cmd('/playsound random.explode @a[rm=0] {} {} {} 16 2'.format(pos.x, pos.y, pos.z))\
            
            if new_args['entityType'] == 'zmqy:flash': # 闪光弹
                for player in GetPlayerDict().values():
                    playerPos = player.getPos()
                    if playerPos.dim != pos.dim: continue
                    flashLevel = 0
                    maxRange = 80
                    comp = serverApi.GetEngineCompFactory().CreateGame(new_args['src'].id)
                    if comp.CanSee(player.id, new_args['entity'].id, maxRange, True, 60, 60): flashLevel += 0.5  # 60度内 满闪 4 秒
                    if comp.CanSee(player.id, new_args['entity'].id, maxRange, True, 120, 120): flashLevel += 1.5  # 120度内 闪 3.5秒
                    if comp.CanSee(player.id, new_args['entity'].id, maxRange, True, 180, 180): flashLevel += 1  # 180度内 闪 2秒
                    if comp.CanSee(player.id, new_args['entity'].id, maxRange, True, 360, 360): flashLevel += 1  # 360度内 闪 1秒
                    if flashLevel == 0: continue
                    dist = api.getDist(pos, playerPos)
                    if dist > maxRange: dist = maxRange
                    if dist <= 0: dist = 0
                    flashLevel = int(flashLevel * (1 - dist / (maxRange * 2))) # 闪光弹效果随距离衰减 每0.8格衰减0.5% (每格衰减0.625%)
                    self.NotifyToClient(player.id, 'onFlash', {'duration': flashLevel})
            elif new_args['entityType'] == 'zmqy:smoke': # 烟雾弹
                for player in GetPlayerDict().values():
                    playerPos = player.getPos()
                    if playerPos.dim != pos.dim: continue
                    self.NotifyToClient(player.id, 'onSmoke', {'duration': 16, 'pos': (pos.x, pos.y, pos.z)})
            elif new_args['entityType'] == 'zmqy:nade': # 燃烧瓶
                size = 4
                self.fireAreas.append({
                    'particleY': pos.y,
                    'area': (int(pos.x - size), int(pos.y - 2), int(pos.z - size), int(pos.x + size), int(pos.y + 2), int(pos.z + size)),
                    'stopTime': time.time() + 10,
                    'source': new_args['src'].id,
                    'dim': pos.dim
                })
                self.tick1000()
            elif new_args['entityType'] == 'zmqy:shoulei': # 手雷
                range = 6.6
                damage = 18
                api.cmd('/particle minecraft:huge_explosion_emitter {} {} {}'.format(pos.x, pos.y, pos.z))
                for player in GetPlayerDict().values():
                    playerPos = player.getPos()
                    if playerPos.dim != pos.dim: continue
                    dist = api.getDist(pos, playerPos)
                    if dist >= range: continue
                    damageMultiplier = 1 - dist / range
                    if new_args['src'].id == player.id:
                        player.hurt(damageMultiplier * damage, 'override')
                    else:
                        player.hurt(damageMultiplier * damage, 'override', new_args['src'].id)


            self.DestroyEntity(new_args['entity'].id)
        Timer.delay(0.88, func, {
            'src': src,
            'entity': entity,
            'entityType': entityType
        })

            

    def Destroy(self):
        print('=====> GameProps Destroy <=====')
