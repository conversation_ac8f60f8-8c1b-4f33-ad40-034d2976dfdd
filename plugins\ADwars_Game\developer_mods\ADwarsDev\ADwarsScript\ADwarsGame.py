# -*- coding: utf-8 -*-
import copy
import time

import lobbyGame.netgameApi as netgameApi
import apolloCommon.mysqlPool as mysqlPool
import mod.server.extraServerApi as serverApi
from api.api import *
from ADwarsBaseClass import ADwarsBaseClass


class ADwarsGame(ADwarsBaseClass):
    def __init__(self, dim, gameCfg, mapCfg, sys):
        super(ADwarsGame, self).__init__(dim, gameCfg, mapCfg, sys)
        # 在此处定义一些全局变量
        # gameCfg 是定义在本插件的mod.json中读取后的dict
        # mapCfg 是定义在同时加载的以_Map结尾的地图插件的mod.json中读取后的dict
        self.roundState = 'prepare' # 小回合的游戏状态，'prepare'为准备(购买)阶段，'playing'为游戏进行中, 'roundOver'为回合结束(进入倒计时)
        self.stateCountdown = 66666 # roundState的倒计时，单位为秒
        self.shopData = mapCfg.get('shop', {}) #type: dict


    def PlayerCreateEvent(self, player):
        # type: (Player) -> None
        super(ADwarsGame, self).PlayerCreateEvent(player)
        player.can_pvp = False
        player.can_place = False
        player.can_destroy = False
        player.can_pick = False
        player.item_position_map = {
                0: 0,
                1: 1,
                2: 2,
                3: 3,
                4: 4,
                5: 5,
                6: 6,
                7: 7,
                8: 8
            }
        # 在此处定义玩家属性
        player.money = 0  # 玩家初始金钱
        print("player init =======> state "+self.state)

    def PlayerLoadedEvent(self, player):
        super(ADwarsGame, self).PlayerLoadedEvent(player)
        if self.state == "wait" or self.state == "free":
            player.addMenuItem(Item('minecraft:clock', name='§b» §a设置 §b«'),slot=1,tag='settings')


    def HotbarMenuDoubleClickEvent(self, player, tag, item):
        # type: (Player, str, Item) -> None
        self.OpenSettings(player)

    def GameStartInit(self):
        super(ADwarsGame, self).GameStartInit()
        
        # 设置玩家权限
        for player in self.players:
            comp = serverApi.GetEngineCompFactory().CreatePlayer(player.id)
            comp.SetBuildAbility(False)
            comp.SetMineAbility(False)
            comp.SetOpenContainersAbility(False)
            comp.SetOperateDoorsAndSwitchesAbility(False)
            player.can_pvp = True
                
        for team in self.team_class.values():
            # 初始化队伍分数属性
            team.score = 0

        self.toPrepare()

    def GameRepeatTimer(self):
        super(ADwarsGame, self).GameRepeatTimer()
        self.stateCountdown -= 1
        if self.stateCountdown <= 0:
            if self.roundState == 'prepare':
                self.toPlaying()
            elif self.roundState == 'playing':
                self.toRoundOver()
            elif self.roundState == 'roundOver':
                self.toPrepare()
        self.SetGameScoreBoard()

    def SetGameScoreBoard(self):
        """
        设置游戏计分板
        [(漏)购买阶段][1 2 3 4 5] [攻方 4 : 5 守方] [5 4 3 2 1][(币)2300] 
          [(剑)总杀: 14] [(剑)击杀: 2] [01:13] [(拳)总助: 1] [(拳)助攻: 1]
        """
        for player in self.players: # type: Player
            sb = player.ScoreBoard
            sb.addLine('middle', line = 0)
            sb.addText('{} {} {} {} {}'.format(gn(1, '黄'), gn(2, '黄'), gn(3, '黄'), gn(4, '黄'), gn(0, '黄')), 2, line = 0)
            sb.addText('{} {} {} {} {}'.format("§e§l攻方§r", gn(4, '黄'), gu('\ue1af'), gn(5, '蓝'), "§b守方"), 3, line = 0)
            sb.addText('{} {} {} {} {}'.format(gn(0, '蓝'), gn(4, '蓝'), gn(3, '蓝'), gn(2, '蓝'), gn(1, '蓝')), 4, line = 0)
            sb.addLine('middle', line=1)
            sb.addText('{}{}{}'.format(gn('01'), gu('\ue1af'), gn('13')), 1, line = 1)
            sb.send()
            

    def toPrepare(self):
        self.roundState = 'prepare'
        self.stateCountdown = self.map_cfg['prepare_time']
        # 传送玩家
        for team in self.team_list: 
            self.teleportTeamPlayersToBase(team)
        for player in self.players:
            # 禁止玩家移动
            player.setMoveable(False)
            player.setJumpable(False)

    def toPlaying(self):
        self.roundState = 'playing'
        self.stateCountdown = self.map_cfg['round_time']
        for player in self.players:
            # 回合开始时解除
            player.setMoveable(True)
            player.setJumpable(True)

    def toRoundOver(self):
        self.roundState = 'roundOver'
        self.stateCountdown = self.map_cfg['round_over_time']
        for player in self.players:
            pass
    
    def ClientPlayerInventoryOpenEvent(self, player):

        if self.roundState == 'prepare' and False:
            player.actionbar('§c购买时间已过')
            return
        self.OpenShop(player)

    def PlayerUpdateItemPositionEvent(self, player, configDict):
        player.item_position_map = configDict
        player.say("§b§l物品栏配置已加载！")

    def OpenSettings(self, player):
        # type: (Player) -> None
        """Open settings menu - Main menu"""
        # 定义物品配置信息
        item_configs = {
            0: {"name": "近战武器", "icon": "minecraft:iron_sword"},
            1: {"name": "远程武器", "icon": "minecraft:bow"},
            2: {"name": "近战道具", "icon": "minecraft:iron_pickaxe"},
            3: {"name": "TNT", "icon": "minecraft:tnt"},
            5: {"name": "闪光弹", "icon": "zmqy:flash"},
            6: {"name": "手榴弹", "icon": "zmqy:shoulei"},
            7: {"name": "烟雾弹", "icon": "zmqy:flash"},
            8: {"name": "火焰弹", "icon": "zmqy:nade"}
        }

        # > Build main form
        formBuilder = serverApi.GetSystem('ServerFormMod', 'ServerFormServerSystem').getFormBuilder()
        form = formBuilder("物品栏配置")
        form.label("§e点击下方按钮配置各类物品的槽位")
        form.label("§7当前配置预览:")

        # 返回按钮放在第一个
        form.button("§a保存并返回", tag="save_and_back")

        # 显示当前配置
        for item_id, config in item_configs.items():
            current_slot = player.item_position_map.get(item_id, item_id)  # 默认值就是item_id本身
            form.button("§b{} §7- §e槽位: {}".format(config["name"], current_slot), tag=str(item_id))

        def main_callback(args):
            player = args["player"]
            click = args['click']

            if click == "save_and_back":
                # 检查配置是否有重复
                if self.ValidatePlayerConfig(player):
                    self.SavePlayerConfig(player)
                    player.say('§a§l配置已保存！')
                else:
                    player.say('§c§l配置失败！存在重复的槽位配置')
                    self.OpenSettings(player)  # 重新打开设置菜单
                return
            elif click.isdigit():
                item_id = int(click)
                if item_id in item_configs:
                    self.OpenItemSlotSettings(player, item_id, item_configs[item_id])

        form.send(main_callback, player)

    def OpenItemSlotSettings(self, player, item_id, item_config):
        # type: (Player, int, dict) -> None
        """Open individual item slot settings"""
        formBuilder = serverApi.GetSystem('ServerFormMod', 'ServerFormServerSystem').getFormBuilder()
        form = formBuilder("配置 - {}".format(item_config["name"]))

        current_slot = player.item_position_map.get(item_id, item_id)
        form.label("§e当前 {} 位于槽位: §b{}".format(item_config["name"], current_slot))
        form.label("§7选择新的槽位 (0-8):")

        # 返回按钮放在第一个
        form.button("§a返回主菜单", tag="back")

        # 显示所有槽位按钮，标记已占用的槽位
        for slot in range(9):
            # 检查该槽位是否被其他物品占用
            occupied_by = None
            for other_item_id, other_slot in player.item_position_map.items():
                if isinstance(other_item_id, int) and other_slot == slot and other_item_id != item_id:
                    occupied_by = other_item_id
                    break

            if slot == current_slot:
                form.button("§a槽位 {} §7(当前位置)".format(slot), tag="current")
            elif occupied_by is not None:
                # 获取占用物品的名称
                occupied_name = self.GetItemNameById(occupied_by)
                form.button("§c槽位 {} §7(被 {} 占用)".format(slot, occupied_name), tag="swap_{}".format(slot))
            else:
                form.button("§f槽位 {}".format(slot), tag="set_{}".format(slot))

        def slot_callback(args):
            player = args["player"]
            click = args['click']

            if click == "back":
                self.OpenSettings(player)
            elif click == "current":
                player.say("§e该物品已在此槽位")
                self.OpenItemSlotSettings(player, item_id, item_config)
            elif click.startswith("set_"):
                new_slot = int(click.split("_")[1])
                player.item_position_map[item_id] = new_slot
                player.say("§e{} 已临时设置到槽位 {} §7(返回主菜单保存)".format(item_config["name"], new_slot))
                self.OpenSettings(player)
            elif click.startswith("swap_"):
                target_slot = int(click.split("_")[1])
                self.SwapItemSlots(player, item_id, target_slot, item_config)

        form.send(slot_callback, player)

    def SwapItemSlots(self, player, item_id, target_slot, item_config):
        # type: (Player, int, int, dict) -> None
        """Handle slot swapping logic"""
        # 找到占用目标槽位的物品
        occupied_item_id = None
        for other_item_id, other_slot in player.item_position_map.items():
            if isinstance(other_item_id, int) and other_slot == target_slot:
                occupied_item_id = other_item_id
                break

        if occupied_item_id is None:
            # 目标槽位为空，直接设置
            player.item_position_map[item_id] = target_slot
            self.SavePlayerConfig(player)
            player.say("§a§l{} 已设置到槽位 {}".format(item_config["name"], target_slot))
            self.OpenSettings(player)
            return

        occupied_name = self.GetItemNameById(occupied_item_id)
        current_slot = player.item_position_map.get(item_id, item_id)  # 默认值就是item_id本身

        formBuilder = serverApi.GetSystem('ServerFormMod', 'ServerFormServerSystem').getFormBuilder()
        form = formBuilder("槽位冲突")
        form.label("§e槽位 {} 已被 {} 占用".format(target_slot, occupied_name))
        form.label("§7当前 {} 在槽位 {}".format(item_config["name"], current_slot))
        form.label("§a选择操作:")
        form.button("§b交换位置", tag="swap")
        form.button("§c取消", tag="cancel")

        def swap_callback(args):
            player = args["player"]
            click = args['click']

            if click == "swap":
                # 执行交换
                old_slot = player.item_position_map.get(item_id, item_id)  # 默认值就是item_id本身
                player.item_position_map[item_id] = target_slot
                player.item_position_map[occupied_item_id] = old_slot
                player.say("§e{} 和 {} 已临时交换位置 §7(返回主菜单保存)".format(item_config["name"], occupied_name))
                self.OpenSettings(player)
            else:
                self.OpenItemSlotSettings(player, item_id, item_config)

        form.send(swap_callback, player)

    def GetItemNameById(self, item_id):
        # type: (int) -> str
        """Get item name by ID"""
        name_map = {
            0: "近战武器",
            1: "远程武器",
            2: "近战道具",
            3: "TNT",
            5: "闪光弹",
            6: "手榴弹",
            7: "烟雾弹",
            8: "火焰弹"
        }
        return name_map.get(item_id, "未知物品")

    def ValidatePlayerConfig(self, player):
        # type: (Player) -> bool
        """Validate player configuration for duplicates"""
        # 获取所有有效的物品槽位配置
        valid_items = {0, 1, 2, 3, 5, 6, 7, 8}  # 有效的物品ID
        used_slots = set()

        for item_id, slot in player.item_position_map.items():
            if type(item_id) == int and type(slot) == int:
                if isinstance(item_id, int) and item_id in valid_items:
                    if slot in used_slots:
                        return False  # 发现重复槽位
                    used_slots.add(slot)

        # 检查是否所有槽位都在0-8范围内
        for slot in used_slots:
            if not (0 <= slot <= 8):
                return False

        return True

    def SavePlayerConfig(self, player):
        # type: (Player) -> None
        """Save player configuration"""
        self.sys.NotifyToClient(player.id, 'StorageSettingsEvent', {'configDict': player.item_position_map})

    def OpenShop(self, player):
        # type: (Player) -> None
        """打开商店"""
        player.say('打开商店测试')
        bb = player.newButtonBoard('装备商店')
        print(bb)
        print(self.shopData)
        pages = [
            [bb.newRender('item', value['icon'], 11), value['name'], key]
             for key,value in self.shopData.items()
        ]
        for key, value in self.shopData.items():
            bb.newPage(key, self.ShopPageInitializer)
        bb.send(pages)
    

    def BuyButtonClickedCallback(self, player, button, args):
        # type: (Player, dict, dict) -> None
        good = args['good']
        slotType = good['items'][0]['slotType']
        slotPos = good['items'][0]['slotPos']
        maxStack = good['items'][0]['maxStack']
        
        item_in_slot = player.getItem(slotPos, slotType)
        item_info = item_in_slot.info

        if item_info and maxStack == 1:
            # armor && weapon
            deltaMoney = item_info.get('userData', {}).get('price', {}).get("__value__", 0)
            used = item_info.get('userData', {}).get('used', {}).get("__value__", 0)  # type: int
            
            # check if money is enough with the item in inventory
            if player.money + deltaMoney * (1 - used) < good['price']:
                player.bb.close()
                player.say('§c§e购买失败， 余额不足')
                return
            
            # get refund if item is in inventory
            player.money += deltaMoney * (1 - used)

        # check max stack
        if item_info and item_info['count'] >= maxStack and item_info['newItemName'] == good['items'][0]['id'] and maxStack >1: 
            player.bb.close()
            player.say('§c§e购买失败，该物品数量已到达上限')
            return

        if player.money < good['price']:
            player.bb.close()
            player.say('§c§e购买失败，余额不足')
            return

        player.money -= good['price']

        for item in good['items']:
            new_item_count = item.get('count', 1)
            if item_info:
                new_item_count += item_info['count']
                new_item_count = min(new_item_count, maxStack)

            i = Item(
                item['id'],
                item.get('aux', 0),
                new_item_count,
                ench=item.get('enchant', []),
                data={
                    'price': good['price'],
                    'used': False
                }
            )
            t = item.get('slotType', 0)
            p = item.get('slotPos', 0)
            if t == 0:
                p = player.item_position_map.get(p, p)
            player.setItem(i,p,t)
            
        

    def ShopPageInitializer(self,player, pageId):
        # type: (Player, str) -> None
        bb = player.bb
        buttons = [
            bb.newButton(
                text="{} | ${} ".format(good['name'], good['price']),
                renderL=bb.newRender("item", good['icon']),
                callback=self.BuyButtonClickedCallback,
                args={"good": good}
            )

            for good in self.shopData[pageId]["goods"]
        ]

        bb.setPage(pageId,
                   self.shopData[pageId]['name'],
                   buttonList=buttons,
                   subtitle="你的余额: {}".format(player.money)
        )
        

    

    def teleportTeamPlayersToBase(self, team):
        print("teleportTeamPlayersToBase: ", team)
        for i, player in enumerate(self.getMembers(team)):
            player.setPos(Pos(self.team_spawn_pos[team][i], dim=self.dim))


