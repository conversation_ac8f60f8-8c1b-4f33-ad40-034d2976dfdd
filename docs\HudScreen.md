[返回](../README.md)
# HudScreen
- HudScreeUI插件，封装客户端Hud显示操作
- *本插件全部方法已集成至Player类中
---
## Hud提示

### **title**
- 显示title文字

|参数|类型|说明|
|:---:|:---:|:---:|
|text|str|文本内容|

```python
player.title('标题')
```

### **subtitle**
- 显示subtitle文字

|参数|类型|说明|
|:---:|:---:|:---:|
|text|str|文本内容|

```python
player.subtitle('副标题')
```

### **upbar**
- 显示upbar文字(title上方，记分板下方)

|参数|类型|说明|
|:---:|:---:|:---:|
|text|str|文本内容|
|*color|tuple|背景颜色(R,G,B)[0,1]|
|*index|str|位置{1,2}1上2下，默认为2|

```python
player.upbar('提示文本',index=2)
```

### **notice**
- 发送一条notice信息 (类似与原版聊天框，在右侧显示，支持render)

|参数|类型|说明|
|:---:|:---:|:---:|
|*text|str|文本内容("\n"换行)|
|*render|dict|显示控件信息|
|*color|tuple|背景颜色(R,G,B)[0,1]|

```python
player.notice('§l§e提示:\n§r§a你的好友xx发来了一条消息',render={
    'type': 'item',#item/image/text/None
    'content': 'minecraft:iron_ingot',#itmeName/imagePath/text/None
    'enchant': None,#True/False/None
    'aux': None,#int/None
    'num': '6',#str/int/None
})
```

---
## 记分板
- 获取玩家的记分板类
```python
ScoreBoard = player.ScoreBoard()
```
### **addLine**
- 增加一行记分板

|参数|类型|说明|
|:---:|:---:|:---:|
|*mode|str|本行的父锚点left/middle/right(默认)|
|*line|int|Y轴索引,值越小越靠近屏幕上方|

```python
ScoreBoard = player.ScoreBoard()
ScoreBoard.addLine('middle',line=0)
```

### **addText**
- 在记分板的某一行中增加一个文本

|参数|类型|说明|
|:---:|:---:|:---:|
|text|str|显示的文本|
|*index|int|X轴索引,值越小越靠近屏幕左侧|
|*color|tuple|背景颜色(R,G,B,A)[0,1]|
|*line|int|Y轴索引,值越小越靠近屏幕上方|


```python
ScoreBoard = player.ScoreBoard()
ScoreBoard.addLine('middle',line=0)
ScoreBoard.addText('右一黑色半透明',0,line=0)
ScoreBoard.addText('右二透明',1,(0,0,0,0),line=0)
```

### **setLineMode**
- 设置记分板的某一行的父锚点

|参数|类型|说明|
|:---:|:---:|:---:|
|mode|str|父锚点 left/middle/right|
|*line|int|Y轴索引|


```python
ScoreBoard.setLineMode('left',line=0)
```

### **resetLine**
- 清空某一行的所有记分板

|参数|类型|说明|
|:---:|:---:|:---:|
|line|int|Y轴索引|


```python
ScoreBoard.resetLine(line=0)
```

### **setText**
- 设置记分板中某条文字内容

|参数|类型|说明|
|:---:|:---:|:---:|
|text|int|文字内容|
|index|int|X轴索引|
|*line|int|Y轴索引|


```python
ScoreBoard.setText('文字内容',0,line=0)
```

### **setTextColor**
- 设置记分板中某条文字的背景颜色

|参数|类型|说明|
|:---:|:---:|:---:|
|*color|int|背景颜色(R,G,B,A)[0,1]|
|*index|int|X轴索引|
|*line|int|Y轴索引|


```python
ScoreBoard.setTextColor((240,20,22,0.5),2,line=0)
```

### **setTextIndex**
- 设置记分板中某条文字的索引(位置)

|参数|类型|说明|
|:---:|:---:|:---:|
|old_index|int|原X轴索引|
|new_index|int|新X轴索引|
|*line|int|Y轴索引|


```python
ScoreBoard.setTextIndex(0,2,line=0)
```

### **delLine**
- 删除某行记分板

|参数|类型|说明|
|:---:|:---:|:---:|
|*line|int|Y轴索引|


```python
ScoreBoard.delLine(line=0)
```

### **delText**
- 删除某行记分板

|参数|类型|说明|
|:---:|:---:|:---:|
|index|int|X轴索引|
|*line|int|Y轴索引|


```python
ScoreBoard.delText(2,line=0)
```

### **setData**
- 使用元数据设置记分板(不推荐)

|参数|类型|说明|
|:---:|:---:|:---:|
|data|dict|记分板数据元信息字典|

```python
data = {
    0: {
        0: {
            'text': '记分板最左侧文字(纯黑不透明背景)',
            'color': (0,0,0,1)
        },
        1: {
            'text': '记分板左数第2个文字(无背景)',
            'color': (0,0,0,0)
        },
        'mode': 'right'
    },
    1: {
        0: {
            'text': '在记分板第二行居中显示',
            'color': (0,0.5,0,0.5)
        },
        'mode': 'middle'
    },
}
ScoreBoard.setData(data)
```

### **send**
- 将记分板数据同步给玩家

```python
ScoreBoard.send()
```