# -*- coding: utf-8 -*-

from common.mod import Mod
import server.extraServerApi as serverApi

@Mod.Binding(name='Bedwars_4_4_5Mod', version='1.0.0')
class Bedwars_4_4_5Server(object):
    
    @Mod.InitServer()
    def Bedwars_4_4_5ServerInit(self):
        serverApi.RegisterSystem('Bedwars_4_4_5Mod', 'Bedwars_4_4_5ServerSystem', 'Bedwars_4_4_5Script.Bedwars_4_4_5ServerSystem.Bedwars_4_4_5ServerSystem')

    @Mod.DestroyServer()
    def Bedwars_4_4_5Server<PERSON><PERSON>roy(self):
        pass
