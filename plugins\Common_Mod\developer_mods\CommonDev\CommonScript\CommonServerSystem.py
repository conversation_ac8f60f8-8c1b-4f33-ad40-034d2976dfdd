# -*- coding: utf-8 -*-

import random
import time
import lobbyGame.netgameApi as netgameApi
import logout
import apolloCommon.mysqlPool as mysqlPool
import apolloCommon.commonNetgameApi as commonNetgameApi
import server.extraServerApi as serverApi
from api.api import *
import copy

comp = serverApi.GetEngineCompFactory().CreateGame(serverApi.GetLevelId())
comp.SetGameRulesInfoServer({'cheat_info': {'enable': True}})
##此处文档长期未维护
"""
本插件为服务端功能集成插件，所有插件均依赖于此插件，获取全局唯一每个实体及玩家类单例
需要在插件中监听CommonSystemLoaded(Namespace: common,System: game)后获取系统

self.ListenForEvent(NS,ES,'LoadServerAddonScriptsAfter',self,self.LoadServerAddonScriptsAfter,5)
def LoadServerAddonScriptsAfter(self,args):
    self.cSys = serverApi.GetSystem('CommonMod', 'CommonServerSystem')
    playerDict = self.cSys.playerDict
    ...


如需在事件中获取玩家(实体同理)类可使用
player = playerDict['playerId']


也可以对玩家对象进行操作
player.killEnemies = 0 


监听AddPlayerCommonEvent对玩家类的方法进行重写或添加
self.ListenForEvent('common','game','AddPlayerCommonEvent',self,self.AddPlayerCommonEvent,5)
def AddPlayerCommonEvent(self,args):
    player = args['player']
    def getLabelName(cls):
        comp = serverApi.GetEngineCompFactory().CreateName(cls.id)
        return 'label' + comp.GetName()
    player.getName = getLabelName
    
    
本插件也会将对游戏的基础逻辑进行优化，将废弃部分事件接及口，重写广播或创建接口

具体事件如下:
AddPlayerCommonEvent - 玩家类创建时触发(player(Player))
AddEntityCommonEvent - 实体类(不包括玩家)创建时触发(entity(Entity))
ClientLoadedCommonEvent - 玩家客户端加载完成后触发[可传送,可发包](player(Player),__id__(str))
PlayerHurtCommonEvent - 玩家攻击玩家触发(attacker(Player),hurter(Player),cause(int[enum]),projectileId(int[enum]),damage(int[total]]),varDamage(int[var]))
PlayerDeathEvent - 玩家死亡(假死)触发(killer(Player or None),attacker(Player or None),hurter(Player),cause(int[enum]),projectileId(int[enum]),damage(int[var]),damageHistory(dict[Player:dmg<30s>],safeTime(float[s]))
PlayerChatEvent - 玩家通过聊天框发送文字时由common转发，主要解决禁言问题(msg(str),player(Player),cancel(bool))

具体接口如下:
self.cSys.xxx()

具体变量如下:
playerDict = self.cSys.playerDict
worldDict = self.cSys.worldDict

对Player对象更改如下:
新增lastHurtBy,hurtHistory,hurtHistoryTimeTemp

"""

ServerSystem = serverApi.GetServerSystemCls()


class CommonServerSystem(ServerSystem):

    def __init__(self, namespace, systemName):
        ServerSystem.__init__(self, namespace, systemName)
        print('=====> Common init <=====')
        NS = serverApi.GetEngineNamespace()
        ES = serverApi.GetEngineSystemName()
        self.mlevelId = serverApi.GetLevelId()
        self.mserverId = lobbyGameApi.GetServerId()
        self.ListenForEvent(NS, ES, 'OnScriptTickServer', self, self.OnScriptTickServer, 5)
        self.ListenForEvent(NS, ES, 'PlayerAttackEntityEvent', self, self.PlayerAttackEntityEvent, 0)
        self.ListenForEvent(NS, ES, 'ActorHurtServerEvent', self, self.ActorHurtServerEvent, 0)
        self.ListenForEvent(NS, ES, 'DamageEvent', self, self.DamageEvent, 1)
        self.ListenForEvent(NS, ES, 'ActuallyHurtServerEvent', self, self.ActuallyHurtServerEvent, 0)
        self.ListenForEvent(NS, ES, 'DelServerPlayerEvent', self, self.DelServerPlayerEvent, 9)
        self.ListenForEvent(NS, ES, 'AddServerPlayerEvent', self, self.AddServerPlayerEvent, 9)
        self.ListenForEvent(NS, ES, 'ServerChatEvent', self, self.ServerChatEvent, 5)
        self.ListenForEvent(NS, ES, 'PlayerJoinMessageEvent', self, self.PlayerJoinMessageEvent, 5)
        self.ListenForEvent(NS, ES, 'PlayerLeftMessageServerEvent', self, self.PlayerLeftMessageServerEvent, 5)
        self.ListenForEvent(NS, ES, 'ProjectileDoHitEffectEvent', self, self.ProjectileDoHitEffectEvent, 5)
        self.ListenForEvent(NS, ES, 'ExplosionServerEvent', self, self.ExplosionServerEvent, 5)
        self.ListenForEvent(NS, ES, 'CommandEvent', self, self.CommandEvent, 5)
        self.ListenForEvent(NS, ES, 'HealthChangeBeforeServerEvent', self, self.HealthChangeBeforeServerEvent, 5)
        self.ListenForEvent(NS, ES, 'ClientLoadAddonsFinishServerEvent', self, self.ClientLoadAddonsFinishServerEvent, 5)
        self.ListenForEvent('common', 'client', 'ClientLoaded', self, self.ClientLoaded, 5)
        # self.ListenForEvent('common', 'client', 'ClientLoaded', self, self.ClientLoaded, 5) 7.18 栈桥竞技场bug留念
        self.ListenForEvent('CommonMod', 'CommonClientSystem', 'BuyItemPaySuccess', self, self.BuyItemPaySuccess, 5)
        self.ListenForEvent('neteaseShop', 'neteaseShopDev', 'ServerShipItemsEvent', self, self.ServerShipItemsEvent, 5)

        # 外接部分
        self.playerDict = {}
        self.worldDict = {}
        self.resetPlayer = True
        # 内部变量
        self.KnockBTemp = {}  # hurter -> knockLevel
        self.HurtTemp = {}  # hurter -> attacker
        self.notifyTemp = set()  # hurters
        self.hurtCD = 10  # 攻击间隔
        self.kbPower = 0.39 # 击退平面初速度
        self.kbHeight = 0.442  # 击退向上初速度
        self.kbgravity = 0.085 # 击退后1.5倍攻击间隔时间内重力

        comp1 = serverApi.GetEngineCompFactory().CreateGame(self.mlevelId)
        comp1.SetHurtCD(self.hurtCD)

        comp2 = serverApi.GetEngineCompFactory().CreatePet(self.mlevelId)
        comp2.Disable()

        Timer.repeat(5, self.HealthAdd)

        comp = serverApi.GetEngineCompFactory().CreateCommand(self.mlevelId)
        comp.SetDefaultPlayerPermissionLevel(1)

    def BuyItemPaySuccess(self, args):
        neteaseShopServerSystem = serverApi.GetSystem("neteaseShop", "neteaseShopDev")
        uid = Pl(args['__id__']).getUid() #传入的参数是玩家的uid
        neteaseShopServerSystem.StartShipProcess(uid)

    def ServerShipItemsEvent(self, args):
        uid = args["uid"]
        entities = args["entities"]
        tradeSystem = serverApi.GetSystem("neteaseTrade", "neteaseTradeDev")
        for entity in entities:
            itemNum = entity['item_num']
            cmd = str(api.uniToUtf(entity['cmd']))
            goods = cmd.split('*')[0]
            num = int(cmd.split('*')[1]) * itemNum
            if goods == 'paper':
                tradeSystem.UpdatePlayerDoughs(uid, {
                    'paper': num
                })
                player = Pl(lobbyGameApi.GetPlayerIdByUid(uid))
                text = '§f状态信息: §a充值成功\n'\
                '§f订单编号: §b{}\n'\
                '§f充值内容: §e{}逐梦券\n'\
                '§6--感谢您的支持，祝您游戏愉快！--\n'\
                '§7*请您截屏保存此界面，以便后续核对充值信息\n'.format(entity['orderid'], num)
                player.newServerForm('充值结果').label(text).button('我知道了').send()
                
                neteaseShopServerSystem = serverApi.GetSystem("neteaseShop", "neteaseShopDev")
                neteaseShopServerSystem.ShipOrderSuccess(args)
                mysqlPool.AsyncExecuteWithOrderKey('p', 'UPDATE `Player_Info` SET total_pay = total_pay + %s WHERE uid = %s', (num, uid, ), None)
            elif goods == 'double_paper':
                def callback(result):
                    tradeSystem = serverApi.GetSystem("neteaseTrade", "neteaseTradeDev")
                    if result and result[0]: # 错误
                        if result[0][0] == 0: # 是首充
                            tradeSystem.UpdatePlayerDoughs(uid, {
                                'paper': num * 2
                            })
                            player = Pl(lobbyGameApi.GetPlayerIdByUid(uid))
                            text = '§f状态信息: §a充值成功(首充双倍)\n'\
                            '§f订单编号: §b{}\n'\
                            '§f充值内容: §e{}逐梦券\n'\
                            '§6--感谢您的支持，祝您游戏愉快！--\n'\
                            '§7*请您截屏保存此界面，以便后续核对充值信息\n'\
                            '§7*您的首充双倍次数以用完，购买其他“首充双倍”栏中的商品将不再享受首充双倍福利\n'.format(entity['orderid'], num * 2)
                            player.newServerForm('充值结果').label(text).button('我知道了').send()
                            mysqlPool.AsyncExecuteWithOrderKey('p', 'UPDATE `Player_Info` SET total_pay = total_pay + %s WHERE uid = %s', (num * 2, uid, ), None)
                        else: # 不是首充
                            tradeSystem.UpdatePlayerDoughs(uid, {
                                'paper': num
                            })
                            player = Pl(lobbyGameApi.GetPlayerIdByUid(uid))
                            text = '§f状态信息: §a充值成功§c(非首充双倍)\n'\
                            '§f订单编号: §b{}\n'\
                            '§f充值内容: §e{}逐梦券\n'\
                            '§6--感谢您的支持，祝您游戏愉快！--\n'\
                            '§7*请您截屏保存此界面，以便后续核对充值信息\n'\
                            '§7*您的首充双倍次数以用完，本次充值不享受首充双倍福利\n'.format(entity['orderid'], num)
                            player.newServerForm('充值结果').label(text).button('我知道了').send()
                            mysqlPool.AsyncExecuteWithOrderKey('p', 'UPDATE `Player_Info` SET total_pay = total_pay + %s WHERE uid = %s', (num, uid, ), None)
                        neteaseShopServerSystem = serverApi.GetSystem("neteaseShop", "neteaseShopDev")
                        neteaseShopServerSystem.ShipOrderSuccess(args)
                    else:
                        text = '§f§l=======================\n'\
                        '§f-->§d状态信息: §c充值失败(首充双倍)\n'\
                        '§f-->§d订单编号: §b{}\n'\
                        '§f-->§d充值内容: §e{}逐梦券\n'\
                        '§6--请截屏后联系服务器管理员解决[01]--\n'\
                        '§f§l=======================\n\n'.format(entity['orderid'],num)
                        player = Pl(lobbyGameApi.GetPlayerIdByUid(uid))
                        player.say(text)
                        player.newServerForm('充值结果').label(text).button('我知道了').send()

                mysqlPool.AsyncQueryWithOrderKey('p', 'SELECT total_pay from `Player_Info` WHERE uid = %s', (uid, ), callback)
                
                
        

    def HealthChangeBeforeServerEvent(self, args):
        if args['entityId'] in self.playerDict:
            if args['to'] <= 0:
                args['cancel'] = True
                
    def ClientLoadAddonsFinishServerEvent(self,args):
        comp = serverApi.GetEngineCompFactory().CreateChatExtension(args['playerId'])
        comp.Disable()
        # print("netease chat disabled")

    def ExplosionServerEvent(self, args):
        if args['sourceId'] is not None: # 产生实体必须为None（tnt/烈焰弹）
            return
        pos = Pos(args['explodePos'][0], args['explodePos'][1], args['explodePos'][2], args['dimensionId'])
        d = 3.5
        min_pos = Pos(pos.x - d, pos.y - d, pos.z - d, pos.dim)
        max_pos = Pos(pos.x + d, pos.y + d, pos.z + d, pos.dim)
        comp = serverApi.GetEngineCompFactory().CreateGame(self.mlevelId)
        entityList = comp.GetEntitiesInSquareArea(None, min_pos.tup, max_pos.tup, pos.dim)
        for eid in entityList:
            if En(eid).getType() == 'minecraft:player':
                player = Pl(eid)
                comp7 = serverApi.GetEngineCompFactory().CreateAction(player.id)
                player_pos = player.getPos()           
                x = player_pos.x - pos.x
                z = player_pos.z - pos.z
                m = float((z ** 2 + x ** 2) ** 0.5)
                p = api.getDist(player_pos, pos) / float(d) # 最远距离分之实际距离，求出一个0到1之间的倍率参数
                if p > 1: p = 1
                if p < 0: p = 0
                p = 10 * p
                if p > 7: p = 7
                v = p
                h = 1 + 0.5 * (1.0 / p)
                # 有方块阻挡
                has_block = serverApi.GetEngineCompFactory().CreateBlockInfo(self.mlevelId).CheckBlockToPos(pos.tup, player_pos.tup, player_pos.dim)
                if has_block: 
                    comp7.SetMobKnockback(x / m, z / m, v / 3.0, h / 3.0, 2)        
                else:
                    comp7.SetMobKnockback(x / m, z / m, v, h, 2)
                    
    def ProjectileDoHitEffectEvent(self, args):
        if args['hitTargetType'] == 'ENTITY' and args['srcId'] in self.playerDict:
            player = Pl(args['srcId'])
            player.cmd('/playsound successful_hit @s')
            player.actionbar('§e命中距离: §l§a{} §r§e格'.format(api.getDist(player.getPos(), En(args['targetId']).getPos())))
            if args['targetId'] in self.playerDict:
                serverApi.GetSystem('AntiCheatingMod', 'AntiCheatingServerSystem').setSafeTime(args['targetId'], int(round(random.randrange(3,6), 3)))

    def ActorHurtServerEvent(self, args):
        try:
            if args['damage_f'] < 0:
                return
            # print self.KnockBTemp, self.HurtTemp
            if args['entityId'] in GetPlayerDict() and args['entityId'] in self.HurtTemp and self.HurtTemp[args['entityId']] in GetPlayerDict():
                hurter = self.Pl(args['entityId'])
                attacker = self.Pl(self.HurtTemp[args['entityId']])
                api.NotifyToGame('PlayerActuallyHurtCommonEvent', {
                    'player': hurter,
                    'attacker': attacker,
                    'damage': args['damage_f']
                })
                attackerPos = attacker.getPos()
                hurterPos = hurter.getPos()
                kr = hurter.getAttr(10) #击退抵抗
                if hurter.id in self.KnockBTemp:
                    power_a = self.KnockBTemp[hurter.id]
                    power_a = 1 + power_a * 0.6
                    # comp7 = serverApi.GetEngineCompFactory().CreateAction(hurter.id)
                    x = hurterPos.x - attackerPos.x
                    z = hurterPos.z - attackerPos.z
                    m = float((z ** 2 + x ** 2) ** 0.5)
                    # comp7.SetMobKnockback(x / m, z / m, self.kbPower * power_a, self.kbHeight, self.kbHeight * 1.1)
                    motionComp = serverApi.GetEngineCompFactory().CreateActorMotion(hurter.id)
                    motionComp.SetPlayerMotion(((x / m) * self.kbPower * power_a * (1-kr) , self.kbHeight * (1-kr), (z / m) * self.kbPower * power_a * (1-kr)))
                    # hurter.setGravity(-self.kbgravity)
                    hurter.last_hurt_time = time.time()
                    serverApi.GetSystem('AntiCheatingMod', 'AntiCheatingServerSystem').setSafeTime(hurter.id, 3.2)
                    itemDict = attacker.getItem(0, ItemPosType.CARRIED).info
                    if itemDict and 'durability' in itemDict and itemDict['durability'] and not (itemDict.get('userData', None) and itemDict['userData'] and itemDict['userData'].get('Unbreakable', None)):
                        if itemDict['durability'] > 1:
                            comp17 = serverApi.GetEngineCompFactory().CreateItem(attacker.id)
                            comp17.SetItemDurability(serverApi.GetMinecraftEnum().ItemPosType.CARRIED, 0, int(round(itemDict['durability'] - 1)))
                        else:
                            attacker.setItem(Item({}), 0, ItemPosType.CARRIED)
                            attacker.cmd('/playsound random.break @s')
                
        except:
            pass

    def HealthAdd(self):
        for player in self.playerDict.values():
            player.setAttr(0, player.getAttr(0) + 1)

    def PlayerJoinMessageEvent(self, args):
        args['cancel'] = True

    def PlayerLeftMessageServerEvent(self, args):
        args['cancel'] = True

    # 外接api
    def Pl(self, playerId):
        # type:(str) -> Player
        return self.playerDict[playerId]

    def CommandEvent(self, args):
        args['cancel'] = True
        player = Pl(args['entityId'])
        cmd = args['command']
        if cmd in ('/hub', '/lobby', '/home'):
            if self.mserverId < 6000:
                player.setPos(Pos(31.50,132.00,-30.50))
                player.setRot((180, 0))
            else:
                lobbyGameApi.TransferToOtherServer(player.id, 'lobby')
        if cmd[:4] == '/hub' and cmd[4:].replace(' ', '').isdigit():
            lobbyGameApi.TransferToOtherServerById(player.id, int('40' + str(int(cmd[4:]) - 1).zfill(2)))
        if cmd == '/info':
            player.say('uid: {}\nentityId: {}\nserverId: {}\ndim: {}'.format(player.getUid(), player.id, self.mserverId, player.getPos().dim))
        if cmd[:7] == '/verify' and len(cmd) > 8:
          token = cmd[7:].replace(" ","")
          uid = player.getUid()
          def _cbk(result):
            # player.say(str(result))
            if result == ():
              player.say("§c无效token或与uid不匹配")
              return
            uid,identify = result[0]
            mysqlPool.AsyncExecuteWithOrderKey('verify','UPDATE Account_Access set verified="true" where uid=%s and identify = %s',(uid,identify))
            player.say("§e绑定成功！")
          mysqlPool.AsyncQueryWithOrderKey('check','select uid,identify from Account_Access where uid=%s and identify = %s',(uid,token),_cbk)

    def ServerChatEvent(self, args):
        player = self.Pl(args['playerId'])
        msg = copy.copy(args['message'])
        if '*kb' in msg:
            msg = msg.split(' ')
            self.hurtCD = int(msg[1])  # 攻击间隔
            self.kbPower = float(msg[2])  # 击退平面初速度
            self.kbHeight = float(msg[3])  # 击退向上初速度
            self.kbgravity = float(msg[4])  # 击退重力
            comp1 = serverApi.GetEngineCompFactory().CreateGame(self.mlevelId)
            comp1.SetHurtCD(self.hurtCD)
            args['cancel'] = True
            return
        isSilent = lobbyGameApi.GetUidIsSilent(player.getUid())
        if isSilent != 2:
            player.say('§e§l>> §c您已被服务器禁言~')
            args['cancel'] = True
            return
        if time.time() - player.last_chat_time < 3:
            args['cancel'] = True
            player.say('§e§l>> §c发言过于频繁...请稍后再试')
            return
        player.last_chat_time = time.time()

        msg = args['message'].replace('狗', '喵').replace('89', '**').replace('64', '**').replace('§', '&')
        for char in copy.deepcopy(msg).decode('utf8'):
            if u'' <= char <= u'':
                msg = msg.replace(api.uniToUtf(char), '??')
        msg = msg[:66]

        api.NotifyToGame('PlayerChatEvent', {
            'msg': msg,
            'player': player,
            'args': args
        })

        if not args['cancel']: # 如果上面没cancel
            args['cancel'] = True
            api.say('§f<{}§r§f> §r{}'.format(player.getName(), msg,))

    def ResetPlayerFunc(self, player):  # type:(Player) -> None
        player.setAttr(0, player.getMaxAttr(0))
        player.setAttr(4, player.getMaxAttr(4))
        effects = player.getEffects()
        if effects:
            for effect in effects:
                player.removeEffect(effect['effectName'])
        comp14 = serverApi.GetEngineCompFactory().CreateAttr(player.id)
        comp14.SetEntityOnFire(0, 0)

    # 内部事件
    def AddServerPlayerEvent(self, args):
        player = Player(args['id'])
        # 删除玩家背包，重置血量, 清除状态, 恢复槽位
        if self.resetPlayer:
            for i in range(27):
                comp3 = serverApi.GetEngineCompFactory().CreateItem(player.id)
                comp3.SpawnItemToEnderChest({}, i)
            for i in range(36):
                player.setItem(Item(dict()), i, ItemPosType.INVENTORY)
            for i in range(4):
                player.setItem(Item(dict()), i, ItemPosType.ARMOR)
            player.setItem(Item(dict()), 0, ItemPosType.OFFHAND)
            comp12 = serverApi.GetEngineCompFactory().CreatePlayer(player.id)
            comp12.ChangeSelectSlot(0)
        self.playerDict[args['id']] = player
        player.setMaxAttr(Attr.health, 20)
        self.ResetPlayerFunc(player)
        player.safe_time = time.time()
        player.attackers = dict()  # pid : time
        player.last_chat_time = 0
        player.last_hurt_time = 0
        api.NotifyToGame('AddPlayerCommonEvent', {'player': player, 'isTransfer': args['isTransfer']})

    def DelServerPlayerEvent(self, args):  # 兜底
        del self.playerDict[args['id']]
        if args['id'] in self.HurtTemp: del self.HurtTemp[args['id']]
        if args['id'] in self.KnockBTemp: del self.KnockBTemp[args['id']]

    def ClientLoaded(self, args):
        api.NotifyToGame('ClientLoadedCommonEvent', {'player': self.Pl(args['__id__']), '__id__': args['__id__']})
        self.Pl(args['__id__']).cmd('/fog @s remove a')
        effects = self.Pl(args['__id__']).getEffects()
        if effects:
            for effect in effects:
                self.Pl(args['__id__']).removeEffect(effect['effectName'])

    def OnScriptTickServer(self):
        Timer.Tick()
        self.KnockBTemp = {}
        self.HurtTemp = {}
        return
        now = time.time()
        for player in self.playerDict.values(): # type: Player
            if player.getGravity() != 0 and player.last_hurt_time + self.hurtCD * 0.02 * 1.5 < now:
                # player.setGravity(0)
                pass
                


    def PlayerAttackEntityEvent(self, args):
        # 如果被上层cancel掉该事件 或 攻击方或受击方不是玩家 则不进行后续操作
        if not args['cancel'] and args['playerId'] in self.playerDict and args['victimId'] in self.playerDict:
            # _serverentitymodule.attack_target(args['playerId'], args['victimId'])#使用隐藏接口进行攻击 
            args['cancel'] = True  # 将其cancel掉 目的是解决 打人断疾跑,疾跑击退距离过长 的问题
            attacker = self.Pl(args['playerId'])
            itemDict = attacker.getItem(0, 2).info  # 获取主手物品
            weapons = {
                'minecraft:wooden_sword': 4,
                'minecraft:stone_sword': 5,
                'minecraft:iron_sword': 6,
                'minecraft:golden_sword': 4,
                'minecraft:diamond_sword': 7,
                'minecraft:netherite_sword': 8,

                'minecraft:wooden_axe': 3,
                'minecraft:stone_axe': 4,
                'minecraft:iron_axe': 5,
                'minecraft:golden_axe': 3,
                'minecraft:diamond_axe': 6,
                'minecraft:netherite_axe': 7,

                'minecraft:wooden_pickaxe': 2,
                'minecraft:stone_pickaxe': 3,
                'minecraft:iron_pickaxe': 4,
                'minecraft:golden_pickaxe': 2,
                'minecraft:diamond_pickaxe': 5,
                'minecraft:netherite_pickaxe': 6,

                'minecraft:wooden_hoe': 2,
                'minecraft:stone_hoe': 3,
                'minecraft:iron_hoe': 4,
                'minecraft:golden_hoe': 2,
                'minecraft:diamond_hoe': 5,
                'minecraft:netherite_hoe': 6,

                'minecraft:wooden_shovel': 2,
                'minecraft:stone_shovel': 3,
                'minecraft:iron_shovel': 4,
                'minecraft:golden_shovel': 2,
                'minecraft:diamond_shovel': 5,
                'minecraft:netherite_shovel': 6,
            }
            dmg = 1
            self.KnockBTemp[args['victimId']] = 0
            try:
                if itemDict['newItemName'] in weapons:  # if None -> None Type has no .. Error
                    dmg = weapons[itemDict['newItemName']]
                if 'book' not in itemDict['newItemName']:
                    ench = itemDict['userData']['ench']  # if None -> keyError
                    for i in ench:
                        if i['id']['__value__'] == 9:  # 锋利
                            lvl = i['lvl']['__value__']
                            dmg = dmg + lvl * 1.25
                        if i['id']['__value__'] == 12: # 击退
                            lvl = i['lvl']['__value__']
                            self.KnockBTemp[args['victimId']] = lvl
                        if i['id']['__value__'] == 13:  # 火焰附加
                            lvl = i['lvl']['__value__']
                            serverApi.GetEngineCompFactory().CreateAttr(args['victimId']).SetEntityOnFire(3 * lvl, 1)
            except:
                pass
            try:
                for effect in attacker.getEffects():
                    if effect['effectName'] == Effect.strength:
                        dmg += dmg * 0.65 * (effect['amplifier'] + 1)
            except:
                pass
            hpos = self.Pl(args['victimId']).getPos()
            
            if -0.4 <= attacker.getMotion()[1] < -0.08:  # 向下移动暴击
                # attacker._title('§c§l' + str(attacker.getMotion()[1]))
                dmg = dmg * 1.5
                attacker.NotifyToClient('CreateEmitter', {
                    'eid': 'minecraft:critical_hit_emitter114514',
                    'pos': (hpos.x, hpos.y + 1.7, hpos.z)
                })
            else:
                # attacker._title('§f' + str(attacker.getMotion()[1]))
                pass
            # attacker.NotifyToClient('CreateEmitter', {
            #     'eid': 'zmqy:damage_emitter',
            #     'pos': (hpos.x, hpos.y + 1.7, hpos.z)
            # })
            self.HurtTemp[args['victimId']] = args['playerId']
            comp5 = serverApi.GetEngineCompFactory().CreateHurt(args['victimId'])
            comp5.Hurt(dmg, 'entity_attack', attacker.id, None, False)

    def DamageEvent(self, args):
        attacker = args['srcId']
        hurter = args['entityId']
        if hurter in self.playerDict and (serverApi.GetEngineCompFactory().CreatePos(hurter).GetPos()[1] < -110 or Pl(hurter).isSpectator):  # 旁观者模式或低于世界最低高度
            args['damage'] = 0
            args['knock'] = False
        if attacker == hurter:  # 自己打自己666
            args['damage'] = 0
            args['knock'] = False
        if hurter in self.playerDict and time.time() < Pl(hurter).safe_time:  # 保护时间
            args['damage'] = 0
            args['knock'] = False
        if args['cause'] in ('lightning', 'fireworks', 'drowning'):  # 免疫伤害形式
            args['damage'] = 0
            args['knock'] = False
        if 'explosion' in args['cause'] and hurter in self.playerDict and attacker:  # 玩家被炸了
            args['damage'] = 0
        if args['cause'] == 'projectile' and args['damage'] >= 1:
            args['damage'] /= 2
            if args['damage'] <= 1: args['damage'] = 1
        if 'explosion' in args['cause']:
            args['knock'] = False
        if attacker in self.playerDict and hurter in self.playerDict and args['cause'] == 'entity_attack':
            args['knock'] = False  # 取消原版击退

    def ActuallyHurtServerEvent(self, args):
        hurter_id = args['entityId']
        attacker_id = args['srcId']
        if hurter_id in self.playerDict:
            hurter = Pl(hurter_id)
            if time.time() < hurter.safe_time:
                if attacker_id in self.playerDict: Pl(attacker_id).upbar('§c保护时间中', index=1)
                args['damage_f'] = 0.0
                return
            api.NotifyToGame('PlayerHurtCommonEvent', {
                'player': hurter,
                'attacker': Pl(attacker_id) if attacker_id in self.playerDict else (En(attacker_id) if attacker_id else None),
                'damage': args['damage_f'],
                'projectile': En(args['projectileId']) if args['projectileId'] else None,
                'cause': args['cause'],
                'args': args
            })

            if attacker_id in self.playerDict:
                hurter.attackers[attacker_id] = time.time()

            if hurter.getAttr(0) <= 0 or hurter.getAttr(0) <= args['damage_f'] or args['cause'] == 'void' and args['damage_f'] != 0.0:  # 寄了
                args['damage_f'] = 0.0
                attackers = set()
                now = time.time()
                last_time = 0
                killer = None
                for k, v in hurter.attackers.items():
                    if now - v <= 30 and k in self.playerDict:
                        attackers.add(Pl(k))
                        if v > last_time:
                            last_time = copy.copy(v)
                            killer = Pl(k)
                hurter.attackers = dict()
                hurter.cmd('/particle minecraft:knockback_roar_particle ~ ~0.8 ~')
                hurter.cmd('/particle minecraft:knockback_roar_particle ~ ~0.8 ~')
                data = {
                    'player': hurter,
                    'killer': killer,
                    'projectile': En(args['projectileId']) if args['projectileId'] else None,
                    'attackers': attackers,
                    'cause': args['cause'],
                    'safe_time': 3
                }
                api.NotifyToGame('PlayerDeathCommonEvent', data)
                # print data['safe_time']
                hurter.safe_time = data['safe_time'] + time.time()
                if killer: 
                    killer.cmd('/playsound random.levelup @s')
                serverApi.GetEngineCompFactory().CreateAttr(hurter.id).SetEntityOnFire(0, 0)
                def func(pl):  # type:(Player) -> None
                    self.ResetPlayerFunc(pl)

                Timer.delay(0, func, hurter)

        if hurter_id in self.playerDict and attacker_id in self.playerDict:  # 攻击者与受击者均为玩家
            hurter = self.Pl(args['entityId'])
            attacker = self.Pl(args['srcId'])
            name = hurter.getName()
            if hasattr(hurter, 'team') and hasattr(hurter.team, 'name'):
                name = '§f[{}§r§f]{} '.format(hurter.team.name, name)
            max_heart = int(round(hurter.getMaxAttr(0)))
            heart = int(round(hurter.getAttr(0) - args['damage_f']))
            if heart < 0: heart = 0
            damage_heart = int(round(args['damage_f']))

            hurter = self.Pl(args['entityId'])
            attacker = self.Pl(args['srcId'])
            heart_text = ''
            if heart % 2 != 0:
                heart_text = api.getHeart(heart - 1) + gu('\ue0f7') + api.getHeart(damage_heart - 1, 1)
            else:
                heart_text = api.getHeart(heart) + api.getHeart(damage_heart, 1)
            count = 0
            count += heart_text.count(gu('\ue0f1'))
            count += heart_text.count(gu('\ue0f2'))
            count += heart_text.count(gu('\ue0f3'))
            count += heart_text.count(gu('\ue0f4'))
            count += heart_text.count(gu('\ue0f5'))
            count += heart_text.count(gu('\ue0f6'))
            count += heart_text.count(gu('\ue0f7'))
            heart_text += api.getHeart((max_heart / 2 - count) * 2, 3)
            attacker.upbar(name + heart_text, index=1)
            

    def Destroy(self):
        logout.info('=====> Common Destroy <=====')
