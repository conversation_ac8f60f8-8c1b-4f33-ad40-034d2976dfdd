# -*- coding: utf-8 -*-

from common.mod import Mod
import server.extraServerApi as serverApi

@Mod.Binding(name='Bedwars_4_4_2Mod', version='1.0.0')
class Bedwars_4_4_2Server(object):
    
    @Mod.InitServer()
    def Bedwars_4_4_2ServerInit(self):
        serverApi.RegisterSystem('Bedwars_4_4_2Mod', 'Bedwars_4_4_2ServerSystem', 'Bedwars_4_4_2Script.Bedwars_4_4_2ServerSystem.Bedwars_4_4_2ServerSystem')

    @Mod.DestroyServer()
    def Bedwars_4_4_2Server<PERSON><PERSON>roy(self):
        pass
