# -*- coding: utf-8 -*-

from common.mod import Mod
import server.extraServerApi as serverApi

@Mod.Binding(name='Bedwars_4_4_1Mod', version='1.0.0')
class Bedwars_4_4_1Server(object):
    
    @Mod.InitServer()
    def Bedwars_4_4_1ServerInit(self):
        serverApi.RegisterSystem('Bedwars_4_4_1Mod', 'Bedwars_4_4_1ServerSystem', 'Bedwars_4_4_1Script.Bedwars_4_4_1ServerSystem.Bedwars_4_4_1ServerSystem')

    @Mod.DestroyServer()
    def Bedwars_4_4_1Server<PERSON><PERSON>roy(self):
        pass
