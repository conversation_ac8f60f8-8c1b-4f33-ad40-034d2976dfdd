# -*- coding: utf-8 -*-
import time
from functools import wraps
import mod.client.extraClientApi as clientApi
ViewBinder = clientApi.GetViewBinderCls()
ClientSystem = clientApi.GetClientSystemCls()
# set类


def setPos(uiNode, path, pos):
    baseUIControl = uiNode.GetBaseUIControl(path)
    baseUIControl.SetPosition(pos)


def setSize(uiNode, path, size):
    baseUIControl = uiNode.GetBaseUIControl(path)
    baseUIControl.SetSize(size)


def setVisible(uiNode, path, Bool):
    baseUIControl = uiNode.GetBaseUIControl(path)
    baseUIControl.SetVisible(Bool)


def setText(uiNode, path, text):
    labelUIControl = uiNode.GetBaseUIControl(path).asLabel()
    labelUIControl.SetText(text)


def setAlpha(uiNode, path, num_range_0_1):
    baseUIControl = uiNode.GetBaseUIControl(path)
    baseUIControl.SetAlpha(num_range_0_1)


def setItem(uiNode, path, itemName, auxValue, enchant):
    itemRendererBaseControl = uiNode.GetBaseUIControl(path)
    itemRendererControl = itemRendererBaseControl.asItemRenderer()
    itemRendererControl.SetUiItem(itemName, auxValue, enchant)


def setImage(uiNode, path, imgPath):
    uiNode.GetBaseUIControl(path).asImage().SetSprite(imgPath)


def setToggle(uiNode, path, Bool):
    switchToggleUIControl = uiNode.GetBaseUIControl(path).asSwitchToggle()
    switchToggleUIControl.SetToggleState(Bool)


def setButton(uiNode, path, func, mode = 'up'):
    buttonUIControl = uiNode.GetBaseUIControl(path).asButton()
    buttonUIControl.AddTouchEventParams({"isSwallow": True})
    if mode == "down":
        buttonUIControl.SetButtonTouchDownCallback(func)
    elif mode == "up":
        buttonUIControl.SetButtonTouchUpCallback(func)
    elif mode == "cancel":
        buttonUIControl.SetButtonTouchCancelCallback(func)
    elif mode in ("hover","hoverIn"):
        buttonUIControl.AddHoverEventParams()
        buttonUIControl.SetButtonHoverInCallback(func)
    elif mode == "hoverOut":
        buttonUIControl.AddHoverEventParams()
        buttonUIControl.SetButtonHoverOutCallback(func)
        
        
# get类

def getPos(uiNode, path):
    baseUIControl = uiNode.GetBaseUIControl(path)
    textPosition = baseUIControl.GetPosition()
    return textPosition


def getSize(uiNode, path):
    baseUIControl = uiNode.GetBaseUIControl(path)
    Size = baseUIControl.GetSize()
    return Size


def getVisible(uiNode, path):
    baseUIControl = uiNode.GetBaseUIControl(path)
    textVisible = baseUIControl.GetVisible()
    return textVisible


def getText(uiNode, path):
    labelUIControl = uiNode.GetBaseUIControl(path).asLabel()
    return labelUIControl.GetText()


def getPath(uiNode, path):
    scrollViewUIControl = uiNode.GetBaseUIControl(path).asScrollView()
    Path = scrollViewUIControl.GetScrollViewContentPath()
    return Path


def resetAnim(uiNode, path):
    setVisible(uiNode, path, False)
    setVisible(uiNode, path, True)
    uiNode.GetBaseUIControl(path).resetAnimation()



# 计时器类


class Timer(object):
    coroutines = {}
    globalEnd = []
    addCoroutines = {}

    # 单例模式
    def __new__(cls, *args, **kwargs):
        if not hasattr(cls, '_instance'):
            cls._instance = super(Timer, cls).__new__(cls, *args, **kwargs)
        return cls._instance

    @classmethod
    def delay(self, time_s, func, *args, **kwargs):
        comp = clientApi.GetEngineCompFactory().CreateGame(clientApi.GetLevelId())
        return comp.AddTimer(time_s, func, *args, **kwargs)

    @classmethod
    def repeat(self, time_s, func, *args, **kwargs):
        comp = clientApi.GetEngineCompFactory().CreateGame(clientApi.GetLevelId())
        return comp.AddRepeatedTimer(time_s, func, *args, **kwargs)

    @classmethod
    def cancel(self, tid):
        comp = clientApi.GetEngineCompFactory().CreateGame(clientApi.GetLevelId())
        return comp.CancelTimer(tid)

    @classmethod
    def timer(func):
        @wraps(func)
        def wrap(*args, **kwargs):
            begin_time = time.perf_counter()
            result = func(*args, **kwargs)
            start_time = time.perf_counter()
            print('func:%r args:[%r, %r] took: %2.4f sec' % (
                func.__name__, args, kwargs, start_time - begin_time))
            return result

        return wrap

    @classmethod
    def StartCoroutine(cls, iter):
        cls.addCoroutines[iter] = 0
        return iter

    @classmethod
    def StopCoroutine(cls, iter):
        cls.globalEnd.append(iter)

    @classmethod
    def Tick(cls):
        if cls.addCoroutines:
            for c, v in cls.addCoroutines.iteritems():
                cls.coroutines[c] = v
        cls.addCoroutines = {}
        if cls.globalEnd:
            for c in cls.globalEnd:
                if cls.coroutines.get(c):
                    del cls.coroutines[c]
            cls.globalEnd = []
        ended = []
        for c, v in cls.coroutines.iteritems():
            try:
                if v < 0:
                    v += 1
                    cls.coroutines[c] = v
                if v == 0 or (v > 0 and time.time() >= v):
                    newv = c.next()
                    if newv > 0:
                        newv = newv + time.time()
                    cls.coroutines[c] = newv
            except StopIteration:
                ended.append(c)
        for c in ended:
            del cls.coroutines[c]


timer = Timer()


class Listener(object):
    @staticmethod
    def Engine(event_name, priority=5):
        def _binding(func):
            func.binding_event_name = event_name
            func.binding_name_space = "Minecraft"
            func.binding_system_name = "Engine"
            func.binding_priority = priority
            return func
        return _binding

    @staticmethod
    def Custom(event_name, system_name="game", name_space="common", priority=5):
        def _binding(func):
            func.binding_event_name = event_name
            func.binding_name_space = name_space
            func.binding_system_name = system_name
            func.binding_priority = priority
            return func
        return _binding

    @staticmethod
    def Server(event_name, system_name="ADwarsServerSystem", name_space="ADwarsMod", priority=5):
        def _binding(func):
            func.binding_event_name = event_name
            func.binding_name_space = name_space
            func.binding_system_name = system_name
            func.binding_priority = priority
            return func
        return _binding

    @staticmethod
    def ListenForEvent(self):
        for key in dir(self):
            func = getattr(self, key)
            if hasattr(func, 'binding_event_name') and type(func.binding_event_name) == str:
                self.ListenForEvent(func.binding_name_space, func.binding_system_name,
                                    func.binding_event_name, self, func, func.binding_priority)

    @staticmethod
    def UnListenForEvent(self):
        for key in dir(self):
            func = getattr(self, key)
            if hasattr(func, 'binding_event_name') and type(func.binding_event_name) == str:
                self.UnListenForEvent(func.binding_name_space, func.binding_system_name,
                                      func.binding_event_name, self, func, func.binding_priority)

# Button子路径
DEFAULT = '/default'
HOVER = '/hover'
PRESSED = '/pressed'
BUTTON_LABEL = '/button_label'

#根目录
RootPath = "/variables_button_mappings_and_controls/safezone_screen_matrix/inner_matrix/safezone_screen_panel/root_screen_panel"

class Event():
    AchievementCompleteEvent = "AchievementCompleteEvent"
    AddEntityClientEvent = "AddEntityClientEvent"
    AddEntityServerEvent = "AddEntityServerEvent"
    AddPlayerAOIClientEvent = "AddPlayerAOIClientEvent"
    AddPlayerCreatedClientEvent = "AddPlayerCreatedClientEvent"
    AddServerPlayerEvent = "AddServerPlayerEvent"
    ChunkAcquireDiscardedClientEvent = "ChunkAcquireDiscardedClientEvent"
    ChunkAcquireDiscardedServerEvent = "ChunkAcquireDiscardedServerEvent"
    ChunkGeneratedServerEvent = "ChunkGeneratedServerEvent"
    ChunkLoadedClientEvent = "ChunkLoadedClientEvent"
    ChunkLoadedServerEvent = "ChunkLoadedServerEvent"
    ClientLoadAddonsFinishServerEvent = "ClientLoadAddonsFinishServerEvent"
    CommandEvent = "CommandEvent"
    DelServerPlayerEvent = "DelServerPlayerEvent"
    EntityRemoveEvent = "EntityRemoveEvent"
    ExplosionServerEvent = "ExplosionServerEvent"
    LoadClientAddonScriptsAfter = "LoadClientAddonScriptsAfter"
    LoadServerAddonScriptsAfter = "LoadServerAddonScriptsAfter"
    NewOnEntityAreaEvent = "NewOnEntityAreaEvent"
    OnCommandOutputClientEvent = "OnCommandOutputClientEvent"
    OnCommandOutputServerEvent = "OnCommandOutputServerEvent"
    OnContainerFillLoottableServerEvent = "OnContainerFillLoottableServerEvent"
    OnLightningLevelChangeServerEvent = "OnLightningLevelChangeServerEvent"
    OnLocalLightningLevelChangeServerEvent = "OnLocalLightningLevelChangeServerEvent"
    OnLocalPlayerStopLoading = "OnLocalPlayerStopLoading"
    OnLocalRainLevelChangeServerEvent = "OnLocalRainLevelChangeServerEvent"
    OnRainLevelChangeServerEvent = "OnRainLevelChangeServerEvent"
    OnScriptTickClient = "OnScriptTickClient"
    OnScriptTickServer = "OnScriptTickServer"
    PlaceNeteaseStructureFeatureEvent = "PlaceNeteaseStructureFeatureEvent"
    PlayerIntendLeaveServerEvent = "PlayerIntendLeaveServerEvent"
    PlayerJoinMessageEvent = "PlayerJoinMessageEvent"
    PlayerLeftMessageServerEvent = "PlayerLeftMessageServerEvent"
    RemoveEntityClientEvent = "RemoveEntityClientEvent"
    RemovePlayerAOIClientEvent = "RemovePlayerAOIClientEvent"
    ServerChatEvent = "ServerChatEvent"
    ServerPostBlockPatternEvent = "ServerPostBlockPatternEvent"
    ServerPreBlockPatternEvent = "ServerPreBlockPatternEvent"
    ServerSpawnMobEvent = "ServerSpawnMobEvent"
    UnLoadClientAddonScriptsBefore = "UnLoadClientAddonScriptsBefore"
    ActorHurtServerEvent = "ActorHurtServerEvent"
    ActuallyHurtServerEvent = "ActuallyHurtServerEvent"
    AddEffectServerEvent = "AddEffectServerEvent"
    ApproachEntityClientEvent = "ApproachEntityClientEvent"
    ChangeSwimStateServerEvent = "ChangeSwimStateServerEvent"
    DamageEvent = "DamageEvent"
    EntityChangeDimensionServerEvent = "EntityChangeDimensionServerEvent"
    EntityDefinitionsEventServerEvent = "EntityDefinitionsEventServerEvent"
    EntityDieLoottableServerEvent = "EntityDieLoottableServerEvent"
    EntityDroppedItemServerEvent = "EntityDroppedItemServerEvent"
    EntityEffectDamageServerEvent = "EntityEffectDamageServerEvent"
    EntityLoadScriptEvent = "EntityLoadScriptEvent"
    EntityModelChangedClientEvent = "EntityModelChangedClientEvent"
    EntityPickupItemServerEvent = "EntityPickupItemServerEvent"
    EntityStartRidingEvent = "EntityStartRidingEvent"
    EntityStopRidingEvent = "EntityStopRidingEvent"
    EntityTickServerEvent = "EntityTickServerEvent"
    HealthChangeBeforeServerEvent = "HealthChangeBeforeServerEvent"
    HealthChangeClientEvent = "HealthChangeClientEvent"
    HealthChangeServerEvent = "HealthChangeServerEvent"
    LeaveEntityClientEvent = "LeaveEntityClientEvent"
    MobDieEvent = "MobDieEvent"
    MobGriefingBlockServerEvent = "MobGriefingBlockServerEvent"
    OnFireHurtEvent = "OnFireHurtEvent"
    OnGroundClientEvent = "OnGroundClientEvent"
    OnGroundServerEvent = "OnGroundServerEvent"
    OnKnockBackServerEvent = "OnKnockBackServerEvent"
    OnMobHitBlockServerEvent = "OnMobHitBlockServerEvent"
    OnMobHitMobClientEvent = "OnMobHitMobClientEvent"
    OnMobHitMobServerEvent = "OnMobHitMobServerEvent"
    ProjectileCritHitEvent = "ProjectileCritHitEvent"
    ProjectileDoHitEffectEvent = "ProjectileDoHitEffectEvent"
    RefreshEffectServerEvent = "RefreshEffectServerEvent"
    RemoveEffectServerEvent = "RemoveEffectServerEvent"
    SpawnProjectileServerEvent = "SpawnProjectileServerEvent"
    StartRidingClientEvent = "StartRidingClientEvent"
    StartRidingServerEvent = "StartRidingServerEvent"
    WillAddEffectServerEvent = "WillAddEffectServerEvent"
    WillTeleportToServerEvent = "WillTeleportToServerEvent"
    AddExpEvent = "AddExpEvent"
    AddLevelEvent = "AddLevelEvent"
    ChangeLevelUpCostServerEvent = "ChangeLevelUpCostServerEvent"
    DimensionChangeClientEvent = "DimensionChangeClientEvent"
    DimensionChangeFinishClientEvent = "DimensionChangeFinishClientEvent"
    DimensionChangeFinishServerEvent = "DimensionChangeFinishServerEvent"
    DimensionChangeServerEvent = "DimensionChangeServerEvent"
    ExtinguishFireClientEvent = "ExtinguishFireClientEvent"
    ExtinguishFireServerEvent = "ExtinguishFireServerEvent"
    GameTypeChangedClientEvent = "GameTypeChangedClientEvent"
    GameTypeChangedServerEvent = "GameTypeChangedServerEvent"
    OnPlayerHitBlockClientEvent = "OnPlayerHitBlockClientEvent"
    OnPlayerHitBlockServerEvent = "OnPlayerHitBlockServerEvent"
    PerspChangeClientEvent = "PerspChangeClientEvent"
    PlayerAttackEntityEvent = "PlayerAttackEntityEvent"
    PlayerCheatSpinAttackServerEvent = "PlayerCheatSpinAttackServerEvent"
    PlayerDieEvent = "PlayerDieEvent"
    PlayerDoInteractServerEvent = "PlayerDoInteractServerEvent"
    PlayerEatFoodServerEvent = "PlayerEatFoodServerEvent"
    PlayerHurtEvent = "PlayerHurtEvent"
    PlayerInteractServerEvent = "PlayerInteractServerEvent"
    PlayerRespawnEvent = "PlayerRespawnEvent"
    PlayerRespawnFinishServerEvent = "PlayerRespawnFinishServerEvent"
    PlayerSleepServerEvent = "PlayerSleepServerEvent"
    PlayerSpinAttackServerEvent = "PlayerSpinAttackServerEvent"
    PlayerStopSleepServerEvent = "PlayerStopSleepServerEvent"
    PlayerTeleportEvent = "PlayerTeleportEvent"
    PlayerTrySleepServerEvent = "PlayerTrySleepServerEvent"
    ServerPlayerGetExperienceOrbEvent = "ServerPlayerGetExperienceOrbEvent"
    StoreBuySuccServerEvent = "StoreBuySuccServerEvent"
    BlockDestroyByLiquidServerEvent = "BlockDestroyByLiquidServerEvent"
    BlockLiquidStateChangeAfterServerEvent = "BlockLiquidStateChangeAfterServerEvent"
    BlockLiquidStateChangeServerEvent = "BlockLiquidStateChangeServerEvent"
    BlockNeighborChangedServerEvent = "BlockNeighborChangedServerEvent"
    BlockRandomTickServerEvent = "BlockRandomTickServerEvent"
    BlockRemoveServerEvent = "BlockRemoveServerEvent"
    BlockSnowStateChangeAfterServerEvent = "BlockSnowStateChangeAfterServerEvent"
    BlockSnowStateChangeServerEvent = "BlockSnowStateChangeServerEvent"
    BlockStrengthChangedServerEvent = "BlockStrengthChangedServerEvent"
    ChestBlockTryPairWithServerEvent = "ChestBlockTryPairWithServerEvent"
    ClientBlockUseEvent = "ClientBlockUseEvent"
    CommandBlockContainerOpenEvent = "CommandBlockContainerOpenEvent"
    CommandBlockUpdateEvent = "CommandBlockUpdateEvent"
    DestroyBlockEvent = "DestroyBlockEvent"
    DirtBlockToGrassBlockServerEvent = "DirtBlockToGrassBlockServerEvent"
    EntityPlaceBlockAfterServerEvent = "EntityPlaceBlockAfterServerEvent"
    FallingBlockBreakServerEvent = "FallingBlockBreakServerEvent"
    FallingBlockCauseDamageBeforeClientEvent = "FallingBlockCauseDamageBeforeClientEvent"
    FallingBlockCauseDamageBeforeServerEvent = "FallingBlockCauseDamageBeforeServerEvent"
    FallingBlockReturnHeavyBlockServerEvent = "FallingBlockReturnHeavyBlockServerEvent"
    FarmBlockToDirtBlockServerEvent = "FarmBlockToDirtBlockServerEvent"
    GrassBlockToDirtBlockServerEvent = "GrassBlockToDirtBlockServerEvent"
    HeavyBlockStartFallingServerEvent = "HeavyBlockStartFallingServerEvent"
    HopperTryPullInServerEvent = "HopperTryPullInServerEvent"
    HopperTryPullOutServerEvent = "HopperTryPullOutServerEvent"
    OnAfterFallOnBlockClientEvent = "OnAfterFallOnBlockClientEvent"
    OnAfterFallOnBlockServerEvent = "OnAfterFallOnBlockServerEvent"
    OnBeforeFallOnBlockServerEvent = "OnBeforeFallOnBlockServerEvent"
    OnEntityInsideBlockClientEvent = "OnEntityInsideBlockClientEvent"
    OnEntityInsideBlockServerEvent = "OnEntityInsideBlockServerEvent"
    OnModBlockNeteaseEffectCreatedClientEvent = "OnModBlockNeteaseEffectCreatedClientEvent"
    OnStandOnBlockClientEvent = "OnStandOnBlockClientEvent"
    OnStandOnBlockServerEvent = "OnStandOnBlockServerEvent"
    PistonActionServerEvent = "PistonActionServerEvent"
    PlayerTryDestroyBlockClientEvent = "PlayerTryDestroyBlockClientEvent"
    ServerBlockEntityTickEvent = "ServerBlockEntityTickEvent"
    ServerBlockUseEvent = "ServerBlockUseEvent"
    ServerEntityTryPlaceBlockEvent = "ServerEntityTryPlaceBlockEvent"
    ServerPlaceBlockEntityEvent = "ServerPlaceBlockEntityEvent"
    ServerPlayerTryDestroyBlockEvent = "ServerPlayerTryDestroyBlockEvent"
    ShearsDestoryBlockBeforeClientEvent = "ShearsDestoryBlockBeforeClientEvent"
    ShearsDestoryBlockBeforeServerEvent = "ShearsDestoryBlockBeforeServerEvent"
    StartDestroyBlockClientEvent = "StartDestroyBlockClientEvent"
    StartDestroyBlockServerEvent = "StartDestroyBlockServerEvent"
    StepOffBlockClientEvent = "StepOffBlockClientEvent"
    StepOffBlockServerEvent = "StepOffBlockServerEvent"
    StepOnBlockClientEvent = "StepOnBlockClientEvent"
    StepOnBlockServerEvent = "StepOnBlockServerEvent"
    ActorAcquiredItemClientEvent = "ActorAcquiredItemClientEvent"
    ActorAcquiredItemServerEvent = "ActorAcquiredItemServerEvent"
    ActorUseItemClientEvent = "ActorUseItemClientEvent"
    ActorUseItemServerEvent = "ActorUseItemServerEvent"
    AnvilCreateResultItemAfterClientEvent = "AnvilCreateResultItemAfterClientEvent"
    ClientItemTryUseEvent = "ClientItemTryUseEvent"
    ClientItemUseOnEvent = "ClientItemUseOnEvent"
    ClientShapedRecipeTriggeredEvent = "ClientShapedRecipeTriggeredEvent"
    ContainerItemChangedServerEvent = "ContainerItemChangedServerEvent"
    CraftItemOutputChangeServerEvent = "CraftItemOutputChangeServerEvent"
    FurnaceBurnFinishedServerEvent = "FurnaceBurnFinishedServerEvent"
    GrindStoneRemovedEnchantClientEvent = "GrindStoneRemovedEnchantClientEvent"
    InventoryItemChangedClientEvent = "InventoryItemChangedClientEvent"
    InventoryItemChangedServerEvent = "InventoryItemChangedServerEvent"
    ItemReleaseUsingClientEvent = "ItemReleaseUsingClientEvent"
    ItemReleaseUsingServerEvent = "ItemReleaseUsingServerEvent"
    ItemUseAfterServerEvent = "ItemUseAfterServerEvent"
    ItemUseOnAfterServerEvent = "ItemUseOnAfterServerEvent"
    OnCarriedNewItemChangedClientEvent = "OnCarriedNewItemChangedClientEvent"
    OnCarriedNewItemChangedServerEvent = "OnCarriedNewItemChangedServerEvent"
    OnItemPutInEnchantingModelServerEvent = "OnItemPutInEnchantingModelServerEvent"
    OnNewArmorExchangeServerEvent = "OnNewArmorExchangeServerEvent"
    OnOffhandItemChangedServerEvent = "OnOffhandItemChangedServerEvent"
    OnPlayerActiveShieldServerEvent = "OnPlayerActiveShieldServerEvent"
    OnPlayerBlockedByShieldAfterServerEvent = "OnPlayerBlockedByShieldAfterServerEvent"
    OnPlayerBlockedByShieldBeforeServerEvent = "OnPlayerBlockedByShieldBeforeServerEvent"
    PlayerDropItemServerEvent = "PlayerDropItemServerEvent"
    PlayerTryDropItemClientEvent = "PlayerTryDropItemClientEvent"
    ServerItemTryUseEvent = "ServerItemTryUseEvent"
    ServerItemUseOnEvent = "ServerItemUseOnEvent"
    ServerPlayerTryTouchEvent = "ServerPlayerTryTouchEvent"
    ShearsUseToBlockBeforeServerEvent = "ShearsUseToBlockBeforeServerEvent"
    StartUsingItemClientEvent = "StartUsingItemClientEvent"
    StopUsingItemClientEvent = "StopUsingItemClientEvent"
    UIContainerItemChangedServerEvent = "UIContainerItemChangedServerEvent"
    AttackAnimBeginClientEvent = "AttackAnimBeginClientEvent"
    AttackAnimBeginServerEvent = "AttackAnimBeginServerEvent"
    AttackAnimEndClientEvent = "AttackAnimEndClientEvent"
    AttackAnimEndServerEvent = "AttackAnimEndServerEvent"
    JumpAnimBeginServerEvent = "JumpAnimBeginServerEvent"
    WalkAnimBeginClientEvent = "WalkAnimBeginClientEvent"
    WalkAnimBeginServerEvent = "WalkAnimBeginServerEvent"
    WalkAnimEndClientEvent = "WalkAnimEndClientEvent"
    WalkAnimEndServerEvent = "WalkAnimEndServerEvent"
    AttackAnimBeginClientEvent = "AttackAnimBeginClientEvent"
    AttackAnimBeginServerEvent = "AttackAnimBeginServerEvent"
    AttackAnimEndClientEvent = "AttackAnimEndClientEvent"
    AttackAnimEndServerEvent = "AttackAnimEndServerEvent"
    JumpAnimBeginServerEvent = "JumpAnimBeginServerEvent"
    WalkAnimBeginClientEvent = "WalkAnimBeginClientEvent"
    WalkAnimBeginServerEvent = "WalkAnimBeginServerEvent"
    WalkAnimEndClientEvent = "WalkAnimEndClientEvent"
    WalkAnimEndServerEvent = "WalkAnimEndServerEvent"
    ClientChestCloseEvent = "ClientChestCloseEvent"
    ClientChestOpenEvent = "ClientChestOpenEvent"
    ClientPlayerInventoryCloseEvent = "ClientPlayerInventoryCloseEvent"
    ClientPlayerInventoryOpenEvent = "ClientPlayerInventoryOpenEvent"
    GridComponentSizeChangedClientEvent = "GridComponentSizeChangedClientEvent"
    OnItemSlotButtonClickedEvent = "OnItemSlotButtonClickedEvent"
    PlayerChatButtonClickClientEvent = "PlayerChatButtonClickClientEvent"
    PlayerInventoryOpenScriptServerEvent = "PlayerInventoryOpenScriptServerEvent"
    PopScreenEvent = "PopScreenEvent"
    PushScreenEvent = "PushScreenEvent"
    UiInitFinished = "UiInitFinished"
    OnMusicStopClientEvent = "OnMusicStopClientEvent"
    PlayMusicClientEvent = "PlayMusicClientEvent"
    PlaySoundClientEvent = "PlaySoundClientEvent"
    ClientJumpButtonPressDownEvent = "ClientJumpButtonPressDownEvent"
    ClientJumpButtonReleaseEvent = "ClientJumpButtonReleaseEvent"
    GetEntityByCoordEvent = "GetEntityByCoordEvent"
    GetEntityByCoordReleaseClientEvent = "GetEntityByCoordReleaseClientEvent"
    HoldBeforeClientEvent = "HoldBeforeClientEvent"
    LeftClickBeforeClientEvent = "LeftClickBeforeClientEvent"
    LeftClickReleaseClientEvent = "LeftClickReleaseClientEvent"
    OnBackButtonReleaseClientEvent = "OnBackButtonReleaseClientEvent"
    OnClientPlayerStartMove = "OnClientPlayerStartMove"
    OnClientPlayerStopMove = "OnClientPlayerStopMove"
    OnKeyPressInGame = "OnKeyPressInGame"
    RightClickBeforeClientEvent = "RightClickBeforeClientEvent"
    RightClickReleaseClientEvent = "RightClickReleaseClientEvent"
    TapBeforeClientEvent = "TapBeforeClientEvent"
    TapOrHoldReleaseClientEvent = "TapOrHoldReleaseClientEvent"
    lobbyGoodBuySucServerEvent = "lobbyGoodBuySucServerEvent"

