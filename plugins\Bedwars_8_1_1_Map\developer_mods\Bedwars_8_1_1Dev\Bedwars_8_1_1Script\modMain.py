# -*- coding: utf-8 -*-

from common.mod import Mod
import server.extraServerApi as serverApi

@Mod.Binding(name='Bedwars_8_1_1Mod', version='1.0.0')
class Bedwars_8_1_1Server(object):
    
    @Mod.InitServer()
    def Bedwars_8_1_1ServerInit(self):
        serverApi.RegisterSystem('Bedwars_8_1_1Mod', 'Bedwars_8_1_1ServerSystem', 'Bedwars_8_1_1Script.Bedwars_8_1_1ServerSystem.Bedwars_8_1_1ServerSystem')

    @Mod.DestroyServer()
    def Bedwars_8_1_1Server<PERSON><PERSON>roy(self):
        pass
