# -*- coding: utf-8 -*-

import lobbyGame.netgameApi as netgameApi
import apolloCommon.mysqlPool as mysqlPool
import mod.server.extraServerApi as serverApi
from api.api import *


class ADwarsEventHandler(object):
    def __init__(self, dim, gameCfg, mapCfg, sys):
        print '游戏房间注册成功， dim = ' + str(dim)

    ####################  事件   ####################

    def OnScriptTickServer(self):  # type:() -> None
        """
        每tick触发 30tick = 1s
        :return:
        """

    def GameTickEvent(self):  # type:() -> None
        """
        游戏主循环, 每秒触发一次
        :return:
        """

    def PlayerBornEvent(self, uid, args):
        # type: (int, dict) -> None
        """
        玩家生成时触发，用于控制出生点
        :param uid:
        :param args: 参数 posx, posy, posz, rotx, roty
        :return:
        """

    def PlayerCreateEvent(self, player):
        # type: (Player) -> None
        """
        玩家对象生成时触发，此时可对玩家对象的属性与方法操作
        :param player: 玩家实例
        :return:
        """

    def PlayerLoadedEvent(self, player):
        # type: (Player) -> None
        """
        玩家客户端入服最后一个事件加载完触发，此时可对玩家做任何操作
        :param player:
        :return:
        """

    def PlayerLeaveEvent(self, player):
        # type: (Player) -> None
        """
        玩家离开服务器最后可获取状态触发
        :param player:
        :return:
        """

    def PlayerAttackEvent(self, player, target, args_cancel):
        # type: (Player, str, dict) -> None
        """
        玩家攻击实体时触发
        :param player: 实施攻击的玩家实例
        :param target: 被攻击的实体实例
        :param args_cancel: 是否取消本次攻击
        :return:
        """

    def PlayerHurtEvent(self, player, attacker, damage, cause, projectile, args_damage_f):
        # type: (Player, Player, float, str, Entity, dict) -> None
        """
        玩家攻击玩家触发
        :param player: 被攻击的玩家
        :param attacker: 实施攻击的玩家
        :param damage: 伤害
        :param cause: 攻击类型
        :param projectile: 子弹实例或None
        :param args_damage_f: 可变伤害
        :return:
        """

    def PlayerDeathEvent(self, player, killer, cause, attackers, projectile, args_safe_time):
        # type: (Player, Player, str, set, Entity, dict) -> None
        """
        玩家死亡后触发
        :param player: 死亡的玩家
        :param killer: 杀死该玩家的玩家，此处为30秒内最后攻击玩家的敌人，无人如摔伤则为None
        :param cause: 攻击类型
        :param attackers: 30秒内攻击玩家的敌人
        :param projectile: 子弹实例或None
        :param args_safe_time: 保护时间，尽量设置否则容易出现误判多次死亡(默认3秒)
        :return:
        """

    def PlayerShootEvent(self, player, bullet, isEntity, pos, target, face, args_cancel):
        # type: (Player, Entity, bool, Pos, Player, int, dict) -> None
        """
        投掷物命中目标触发
        :param player: 发射投掷物的玩家, 可能为None
        :param bullet: 字典Entity实例
        :param isEntity: 是否命中的是实体
        :param pos: 实体/方块坐标
        :param target: 命中的玩家, 当且仅当isEntity == True 有效
        :param face: 命中方块的朝向，当且仅当isEntity == False 有效
        :param args_cancel: 是否取消该事件
        :return:
        """

    def TryDestroyBlockEvent(self, player, pos, block, aux, face, args_cancel, spawnResources):
        # type: (Player, Pos, str, int, int, dict, bool) -> None
        """
        玩家尝试破坏方块触发
        :param player: 玩家实例
        :param pos: 坐标
        :param block: 方块id
        :param aux: 方块aux
        :param face: 朝向 Down = 0 Up = 1 North = 2 South = 3 West = 4 East = 5
        :param args_cancel: 是否取消事件执行
        :param spawnResources: 是否生成掉落物
        :return:
        """
        return

    def DestroyBlockEvent(self, player, pos, block, aux, face):
        # type: (Player, Pos, str, int, int) -> None
        """
        玩家破坏了方块触发
        :param player: 玩家实例
        :param pos: 坐标
        :param block: 方块id
        :param aux: 方块aux
        :param face: 朝向 Down = 0 Up = 1 North = 2 South = 3 West = 4 East = 5
        :return:
        """
        return

    def TryPlaceBlockEvent(self, player, pos, block, aux, face, args_cancel):
        # type: (Player, Pos, str, int, int, dict) -> None
        """
        玩家尝试破坏方块触发
        :param player: 玩家实例
        :param pos: 坐标
        :param block: 方块id
        :param aux: 方块aux
        :param face: 朝向 Down = 0 Up = 1 North = 2 South = 3 West = 4 East = 5
        :param args_cancel: 是否取消事件执行
        :return:
        """

    def PlaceBlockEvent(self, player, pos, block, aux, face):
        # type: (Player, Pos, str, int, int) -> None
        """
        玩家破坏方块后触发
        :param player: 玩家实例
        :param pos: 坐标
        :param block: 方块id
        :param aux: 方块aux
        :param face: 朝向 Down = 0 Up = 1 North = 2 South = 3 West = 4 East = 5
        :return:
        """

    def TryPickItemEvent(self, player, itemEntity, item, args_cancel):
        # type: (Player, Entity, Item, dict) -> None
        """
        玩家尝试拾起掉落物触发
        :param player: 玩家实例
        :param itemEntity: 掉落物实体
        :param item: 掉落物
        :param args_cancel: 是否取消事件执行
        :return:
        """

    def TryUseItemEvent(self, player, item, args_cancel):
        # type: (Player, Item, dict) -> None
        """
        玩家右键或长按物品栏物品触发
        :param player: 玩家实例
        :param item: 物品字典
        :param args_cancel: 是否取消事件执行
        :return:
        """

    def ExplosionEvent(self, destroyBlocks, pos, hurtEntityIds, FromEntityId):
        # type: (list, Pos, list, str) -> None
        """
        地图发生爆炸触发
        :param destroyBlocks: 破坏的方块二维列表, [[x,y,z,cancel], [x...]...]
        :param pos: 爆炸位置
        :param hurtEntityIds: 因爆炸受伤的实体列表
        :param FromEntityId: 产生爆炸的实体
        :return:
        """

    def PlayerChatEvent(self, player, msg, args_cancel):
        # type: (Player, str, dict) -> None
        """
        玩家发送聊天栏消息时触发
        :param player:
        :param msg:
        :param args_cancel:
        :return:
        """
    
    def HotbarMenuDoubleClickEvent(self, player, tag, item):
        # type: (Player, str, Item) -> None
        """
        玩家双击物品栏菜单物品时触发
        :param player: 玩家实例
        :param tag: 菜单物品的tag
        :param item: 菜单物品
        :return:
        """

    def ClientPlayerInventoryOpenEvent(self, player):
        # type: (Player) -> None
        """
        玩家打开物品栏时触发
        :param player: 玩家实例
        :return:
        """
    
    def PlayerUpdateItemPositionEvent(self, player, configDict):
        # type: (Player, dict) -> None
        """
        玩家更新物品栏位置映射列表时触发
        :param player: 玩家实例
        :param configDict: 物品栏位置字典
        :return:
        """