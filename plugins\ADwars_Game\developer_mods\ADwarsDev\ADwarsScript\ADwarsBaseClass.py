# -*- coding: utf-8 -*-
import copy

import lobbyGame.netgameApi as netgameApi
import apolloCommon.mysqlPool as mysqlPool
import mod.server.extraServerApi as serverApi
from api.api import *
from ADwarsEventHandler import ADwarsEventHandler


class Team(object):
    def __init__(self, team):
        self.id = team

class ADwarsBaseClass(ADwarsEventHandler):
    def __init__(self, dim, gameCfg, mapCfg, sys):
        super(ADwarsBaseClass, self).__init__(dim, gameCfg, mapCfg, sys)
        self.dim = dim
        self.game_cfg = gameCfg
        self.map_cfg = mapCfg
        self.sys = sys
        self.game_name = mapCfg['game_name']
        self.game_mode = mapCfg['game_mode']
        self.game_version = mapCfg['game_version']
        self.battle_type = mapCfg['battle_type']
        self.max_player = mapCfg['max_player']
        self.max_member = mapCfg['max_member']
        self.min_player = mapCfg['min_player']
        self.max_time = mapCfg['max_time']
        self.map_name = mapCfg['map_name']
        self.map_size = mapCfg['map_size']
        self.team_list = mapCfg['team_list']
        self.spawn_pos = mapCfg['spawn_pos']
        self.team_spawn_pos = mapCfg['team_spawn_pos']
        self.team_color = mapCfg['team_color']
        self.team_aux = mapCfg['team_aux']
        self.team_suffix = mapCfg['team_suffix']
        self.team_leather_color = mapCfg['team_leather_color']
        self.countdown_time = mapCfg['countdown_time']
        self.close_countdown_time = mapCfg['close_countdown_time']
        self.last_countdown_time = mapCfg['last_countdown_time']
        self.room_sys = serverApi.GetSystem('RoomMod', 'RoomServerSystem')
        self.wait_player_maxed = False
        self.extra_info = None
        self.players = set()
        self.all_player = set()
        self.state = 'free'
        self.countdown = 0
        self.team_class = {t: Team(t) for t in self.team_list}
        self.player_placed_block_pos = set()
        self.loadChunks()

    def PlayerBornEvent(self, uid, args):
        args['posx'] = self.spawn_pos[0]
        args['posy'] = self.spawn_pos[1]
        args['posz'] = self.spawn_pos[2]
        if self.state == 'game' or len(self.players) >= self.max_player:
            _Server_System = serverApi.GetServerSystemCls()
            _BCSys = _Server_System('common', 'game')
            _BCSys.NotifyToMaster('KickPlayer', {
                'uid': uid,
                'reason': '游戏已经开始'
            })
        try:
            Pl(lobbyGameApi.GetPlayerIdByUid(uid)).setGamemode(2)
        except:
            pass

    def GameStartInit(self):
        #分队
        self.TeamInit()
        for player in self.players:
            #设置状态
            # player.can_pvp = True
            # player.can_place = True
            # player.can_destroy = True
            # player.can_pick = True
            player.setGamemode(0)
            self.countdown = copy.copy(self.max_time)
            #初始化变量
            player.team.name = self.team_color[player.team.id] + player.team.id
            player.team.fullname = player.team.name + self.team_suffix
            # player.team.spawn_pos = Pos(self.team_spawn_pos[player.team.id][0], self.team_spawn_pos[player.team.id][1], self.team_spawn_pos[player.team.id][2], self.dim)
            player.team.spawn_pos = list([Pos(x[0], x[1], x[2], self.dim) for x in self.team_spawn_pos[player.team.id]])
            player.state = 'game'
            player.setExtraNameTag('§f[{}§r§f]'.format(player.team.name), -10)
            player.say('§e>> §b{} §a游戏开始!'.format(self.game_name))
            player.say('§e>> §f地图: {}   §f队伍: §f{}'.format(self.map_name, player.team.fullname))
            #传送
            # player.setPos(player.team.spawn_pos)
            #衣服
            self.RefreshPlayerArmor(player)

    def SetScoreBoard(self):
        if self.state in ('wait', 'count'):
            for player in self.players: # type: Player
                sb = player.ScoreBoard
                sb.addLine('right', line = 0)
                sb.addText('{} {}'.format(gu('\ue037'), self.map_name), 0, line = 0)
                sb.addText('{} {}'.format(gu('\ue031'), self.game_mode), 1, line = 0)
                if self.state == 'count': sb.addText('{} {}'.format(gu('\ue018'), api.formatTime(self.countdown)), 2, line = 0)
                sb.addLine('middle', line=1)
                sb.addText('{} {}{} §f/ {}'.format(gu('\ue038'), '§a' if len(self.players) >= self.min_player else '§c', len(self.players), self.max_player), 3, line = 1)
                sb.send()
                player.setRightText('{} - {}  v{}'.format(self.game_name, self.map_name, self.game_version))

    def PlayerChatEvent(self, player, msg, args_cancel):
        args_cancel['cancel'] = True
        if player in self.players and self.state == 'game':
            if len(self.getMembers(player.team.id)) == 1:
                for _pl in self.all_player:
                    _pl.say('§f<{}>§f[{}§f]§f{}: §f{}'.format('喊话', player.team.name, player.getName(), msg))
                return
            if msg[0] == '!' or msg[:2] == '！' or msg[0] == '@':
                front = msg[:2].replace('!', '').replace('！', '').replace('@', '')
                for _pl in self.all_player:
                    _pl.say('§f<{}>§f[{}§f]§f{}: §f{}'.format('喊话', player.team.name, player.getName(), front + msg[2:]))
            else:
                for _pl in self.players:
                    if _pl.team is player.team:
                        _pl.say('§f<{}>§f[{}§f]§f{}: §f{}'.format('本队', player.team.name, player.getName(), msg))
        else:
            for _pl in self.all_player:
                _pl.say('§7<{}>{}: {}'.format('旁观者', player.getName(), msg))

    def PlayerAttackEvent(self, player, target, args_cancel):
        if not player.can_pvp:
            args_cancel['cancel'] = True
        if self.state != 'game': return
        if target in self.players and player.team is target.team:
            args_cancel['cancel'] = True

    def PlayerHurtEvent(self, player, attacker, damage, cause, projectile, args_damage_f):  # type: (Player, Player, float, str, Entity, dict) -> None
        if not attacker.can_pvp:
            args_damage_f['damage_f'] = 0.0
        if self.state != 'game': return
        if player.team is attacker.team:
            args_damage_f['damage_f'] = 0.0

    def PlayerShootEvent(self, player, bullet, isEntity, pos, target, face, args_cancel):
        if not player.can_pvp:
            args_cancel['cancel'] = True
        if self.state != 'game': return
        if target and player.team is target.team:
            args_cancel['cancel'] = True

    def TryPlaceBlockEvent(self, player, pos, block, aux, face, args_cancel):
        if not player.can_place:
            args_cancel['cancel'] = True
        if self.state != 'game': return
        if pos.x < self.map_size[0] or pos.x > self.map_size[3] or pos.y < self.map_size[1] or pos.y > self.map_size[4] or pos.z < self.map_size[2] or pos.z > self.map_size[5]:
            args_cancel['cancel'] = True
            player.actionbar('§c您不能在地图外部放置方块')
        r = 2
        # for p in self.team_spawn_pos.values():
        #     if p[0] + r > pos.x > p[0] - r and p[1] + r > pos.y > p[1] - r and p[2] + r > pos.z > p[2] - r:
        #         args_cancel['cancel'] = True
        #         player.actionbar('§c您不能在队伍出生点附近放置方块')
        self.player_placed_block_pos.add(pos.tup)

    def TryDestroyBlockEvent(self, player, pos, block, aux, face, args_cancel, spawnResources):
        if not player.can_destroy or pos.tup not in self.player_placed_block_pos:
            args_cancel['cancel'] = True
        else:
            self.player_placed_block_pos.discard(pos.tup)

    def TryPickItemEvent(self, player, itemEntity, item, args_cancel):
        if not player.can_pick:
            args_cancel['cancel'] = True

    def ExplosionEvent(self, destroyBlocks, pos, hurtEntityIds, FromEntityId):  # type: (list, Pos, list, str) -> None
        for i, tup in enumerate(destroyBlocks):
            pos = (tup[0], tup[1], tup[2])
            if pos not in self.player_placed_block_pos:
                destroyBlocks[i][3] = True

    def RefreshPlayerArmor(self, player):
        # type: (Player) -> None
        player.setItem(Item('minecraft:leather_helmet', unbreakable=True, lock=True,
                            data={'customColor': {'__type__': 3, '__value__': self.team_leather_color[player.team.id]}}), 0, ItemPosType.ARMOR)
        player.setItem(Item('minecraft:leather_chestplate', unbreakable=True, lock=True,
                            data={'customColor': {'__type__': 3, '__value__': self.team_leather_color[player.team.id]}}), 1, ItemPosType.ARMOR)

    def GameRepeatTimer(self):
        self.countdown -= 1
        if self.countdown <= 0:
            self.GameOver()
        if len(self.players) == 0:
            self.GameOver()

    def CountRepeatTimer(self):
        if self.countdown == self.close_countdown_time:  # 倒计时达到关闭房间入口时间
            self.room_sys.ToGameRoom(self.dim)
        if self.countdown > self.last_countdown_time:  # 正常倒计时
            for player in self.players.copy():
                player.cmd('/title @s title §e')
                player.cmd('/title @s subtitle §l§e{}§a将在 {} 秒后开始'.format(self.game_name, self.countdown))
        else:  # 最后last_countdown_time秒
            for player in self.players:
                player.cmd('/title @s title §e')
                player.cmd('/title @s subtitle §l§b即将传送至战场§7... §g{}§b秒'.format(self.countdown))
                player.cmd('/playsound note.pling @s')
        if self.countdown <= 0:
            self.room_sys.ToGameRoom(self.dim)
            for player in self.players.copy():
                player.cmd('/playsound portal.travel @s')
                player.cmd('/title @s title §e')
                player.cmd('/title @s subtitle §l§7传送中...')
                self.state = 'game'
                self.GameStartInit()
                return
        self.countdown -= 1

    def FreeRepeatTimer(self):
        pass

    def WaitRepeatTimer(self):
        pass

    def OverRepeatTimer(self):
        pass

    def GameTickEvent(self):  # type:() -> None
        playerNum = len(self.players)
        self.SetScoreBoard()
        if self.state == 'free':
            if playerNum:  # 有玩家进入，进入等待状态
                self.state = 'wait'
                self.GameTickEvent()
                return
        elif self.state == 'wait':
            if not playerNum:  # 玩家退出，进入空闲状态
                self.ToFreeRoom()
                self.state = 'free'
                self.GameTickEvent()
                return
            if playerNum >= self.min_player:  # 玩家进入, 玩家数量大于等于最低开始游戏数量，开始倒计时
                for player in self.players:
                    player.say('§e>> §a人数充足，即将开始倒计时')
                self.state = 'count'
                self.countdown = copy.copy(self.countdown_time)
                self.GameTickEvent()
                return
            self.WaitRepeatTimer()
        elif self.state == 'count':
            if not playerNum:  # 玩家退出，进入空闲状态
                self.ToFreeRoom()
                self.state = 'free'
                self.GameTickEvent()
                return
            if playerNum < self.min_player:  # 玩家退出, 玩家数量小于最低开始游戏数量，停止倒计时
                for player in self.players:
                    player.say('§e>> §c人数不足, 倒计时已停止')
                self.room_sys.ToWaitRoom(self.dim)
                self.state = 'wait'
                self.GameTickEvent()
                return
            if playerNum >= self.max_player:  # 玩家数量达到房间满员玩家
                if self.countdown > self.last_countdown_time and not self.wait_player_maxed:
                    self.wait_player_maxed = True
                    for player in self.players:
                        player.say('§e>> §a游戏将在 {} 秒后开始'.format(self.last_countdown_time))
                    self.countdown = self.last_countdown_time
                    self.room_sys.ToGameRoom(self.dim)
            else:
                self.wait_player_maxed = False
            self.CountRepeatTimer()
        elif self.state == 'game':
            self.GameRepeatTimer()
        elif self.state == 'over':
            self.OverRepeatTimer()

    def TeamInit(self):
        # 分队，尽可能先分组队的玩家
        getTeamPlayersTmp = set()
        queue = []
        maxMember = self.max_player / len(self.team_list)
        if len(self.players) > self.max_player:
            for player in self.players:
                player.kickSelf('服务器错误： 玩家人数超出房间最大人数, 请重新进入游戏')
            self.GameOver()
            self.room_sys.ToCloseRoom(self.dim)
            return
        for player in self.players:  # type: Player #遍历玩家
            if player.teamId and player not in getTeamPlayersTmp:  # 如果玩家有队伍并且玩家没被分配
                print player.teamId
                members = self.getPlayers('teamId', player.teamId)
                if len(members) > 1:
                    for member in members:  # 同队玩家
                        getTeamPlayersTmp.add(member)
                    queue.insert(0, members)  # 放队列前面
                else:
                    queue.append({player})  # 放后面最后补位分配
            elif player not in getTeamPlayersTmp:  # 玩家没被分配 没队伍
                queue.append({player})  # 放后面最后补位分配
                getTeamPlayersTmp.add(player)
        print queue
        while queue:
            members = queue.pop(0)
            minNum = 999
            minTeam = ''
            for team in self.team_list:
                num = len(self.getMembers(team))
                if num < minNum:
                    minNum = copy.copy(num)
                    minTeam = team
            if minNum + len(members) <= maxMember:
                for member in members:
                    member.team = self.team_class[minTeam]
            else:
                for member in members:
                    queue.append({member})

    def ToFreeRoom(self):
        def cb(data):
            if not data['suc'] and not len(self.players) and self.state in (0, 1):
                Timer.delay(5, self.ToFreeRoom)

        self.room_sys.ToFreeRoom(self.dim, cb)

    def PlayerLoadedEvent(self, player):  # type: (Player) -> None
        if self.state == 'game' or len(self.players) >= self.max_player:
            player.kickSelf('游戏已开始, 请尝试重新进入服务器')
            return
        self.players.add(player)
        self.all_player.add(player)
        player.setPos(Pos(self.spawn_pos[0],self.spawn_pos[1],self.spawn_pos[2],self.dim))
        player.say('§e§l>>§a§l欢迎您 §r§f{}\n§r§e  您正在逐梦启元服务器体验 §l§6{}'.format(player.getName(), self.game_name))
        player.setGamemode(2)
        for _player in self.players:# type: Player
            _player.notice('§a玩家 §b§l{} §a进入了游戏 ({}/{})'.format(player.getName(), len(self.players), self.max_player))

    def PlayerLeaveEvent(self, player):  # type: (Player) -> None
        self.players.discard(player)
        self.all_player.discard(player)
        for _player in self.players:# type: Player
            _player.notice('§c玩家 §b{} §c退出了游戏'.format(player.getName()))

    def PlayerCreateEvent(self, player):  # type: (Player) -> None
        player.can_pvp = False
        player.can_place = False
        player.can_destroy = False
        player.can_pick = False

    def loadChunks(self):
        def callback(data):
            code = data.get('code', 0)
            if code == 1:
                print '游戏区块加载成功！dim = '  + str(self.dim)
            elif code == 0:
                print '游戏区块加载失败！dim = '  + str(self.dim)
            comp2 = serverApi.GetEngineCompFactory().CreateChunkSource(serverApi.GetLevelId())
            suc = comp2.SetAddArea('main_game_area_' + str(self.dim), self.dim, (self.map_size[0] - 80, self.map_size[1], self.map_size[2] - 80), (self.map_size[3] + 80, self.map_size[4], self.map_size[5] + 80))
            print '游戏常加载设置成功！' if suc else '游戏常加载设置失败！'
        comp1 = serverApi.GetEngineCompFactory().CreateChunkSource(serverApi.GetLevelId())
        comp1.DoTaskOnChunkAsync(self.dim, (self.map_size[0], self.map_size[1], self.map_size[2]), (self.map_size[3], self.map_size[4], self.map_size[5]), callback)

    def getPlayers(self, attr, value):
        re_players = set()
        for player in self.players:
            if hasattr(player, attr) and getattr(player, attr) == value:
                re_players.add(player)
        return re_players

    def getMembers(self, team):
        re_players = set()
        for player in self.players:
            if hasattr(player, 'team') and getattr(player, 'team').id == team:
                re_players.add(player)
        return re_players

    def setPlayerOutOfGame(self, player): # type: (Player) -> None
        print('setPlayerOutOfGame', player.getName())
        player.setSpectator()
        def cb():
            player.cmd('/clear @s')
            player.addMenuItem(Item('minecraft:bed', 14, name='§c返回主城', lock=1), None,'back_to_lobby', 8)
            player.addMenuItem(Item('minecraft:paper', name='§a再来一局', lock=1), None,'play_again', 4)
        Timer.delay(0.5, cb)
    
    def HotbarMenuDoubleClickEvent(self, player, tag, item):
        if 'back_to_lobby' in tag:
            def cb(player, tag, index):
                if index:
                    lobbyGameApi.TransferToOtherServer(player.id, 'lobby')
            player.AlertBoard(text='您确定要离开 {} 返回主城吗？'.format(self.game_name), cb=cb)
        elif 'play_again' in tag:
            def cb(player, tag, index):
                if index:
                    sys = serverApi.GetSystem('RoomMod', 'RoomServerSystem')
                    def cb2(data):  # 注册房间
                        player.AlertBoard(text='§a功能服返回: §b' + str(data['msg']), buttons=['确定'])
                        player.say('§a功能服返回: §b' + str(data['msg']))
                    sys.RequestGameRoom(player.getUid(), self.battle_type, self.extra_info, cb2)
            player.AlertBoard(text='您要再玩一局 {} 吗'.format(self.game_name), cb=cb)
    
    def GameOver(self): # 游戏结束函数
        if self.state != 'game': return
        self.state = 'over'
        for player in self.all_player:
            self.setPlayerOutOfGame(player)
        def func10():
            def cb(player, tag, index):
                if index == 2:
                    lobbyGameApi.TransferToOtherServer(player.id, 'lobby')
                elif index == 1:
                    sys = serverApi.GetSystem('RoomMod', 'RoomServerSystem')
                    def cb2(data):  # 注册房间
                        player.AlertBoard(text='§a功能服返回: §b' + str(data['msg']), buttons=['确定'])
                        player.say('§a功能服返回: §b' + str(data['msg']))
                    sys.RequestGameRoom(player.getUid(), self.battle_type, self.extra_info, cb2)
            for player in self.all_player: # type:Player
                player.AlertBoard('请选择:', '§c游戏已结束', ['§7再等等', '§a再来一局', '§b返回主城'], cb=cb)
        Timer.delay(8, func10)
        def func30():
            for player in self.all_player.copy():
                lobbyGameApi.TransferToOtherServer(player.id, 'lobby')
            entityDicts = serverApi.GetEngineActor()
            for eid in entityDicts: # 杀实体给房间其他维度让性能
                if entityDicts[eid]['dimensionId'] == self.dim:
                    self.sys.DestroyEntity(eid)
        Timer.delay(30, func30)
        def func60(): # 一分钟还没滚的直接踢
            for player in self.all_player.copy():
                player.kickSelf('房间已经关闭')
            self.sys.GameOver(self.dim)
            self.room_sys.ToCloseRoom(self.dim)
        Timer.delay(60, func60)
        for player in self.all_player:
            player.say('§e§l>> §c房间将在30秒后关闭')