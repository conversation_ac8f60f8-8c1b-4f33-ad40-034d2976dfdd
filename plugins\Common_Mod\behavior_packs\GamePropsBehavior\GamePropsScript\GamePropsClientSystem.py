# -*- coding: utf-8 -*-

import mod.client.extraClient<PERSON>pi as clientApi
from ClientApi import *
from uuid import uuid4
ClientSystem = clientApi.GetClientSystemCls()


class GamePropsClientSystem(ClientSystem):
    def __init__(self, namespace, systemName):
        ClientSystem.__init__(self, namespace, systemName)

        Listener.ListenForEvent(self)
        self.mPlayerId = clientApi.GetLocalPlayerId()
        self.mLevelId = clientApi.GetLevelId()
        self.ui = None
        self.smokes = []
        self.flashStopTime = 0

    @Listener.Engine(Event.UiInitFinished)
    def UiInitFinished(self, args):
        clientApi.RegisterUI('GamePropsMod', 'GamePropsUi', "GamePropsScript.GamePropsUi.GamePropsScreen", "GameProps.main")
        self.ui = clientApi.CreateUI('GamePropsMod', 'GamePropsUi', {"isHud": 1, 'data':{},'client':self})
        # clientApi.PushScreen('GamePropsMod', 'GamePropsUi', {'data':{},'client':self})

    @Listener.Server('onFlash')
    def onFlash(self, args):
        # self.ui.onFlash(args['duration'])
        self.flashStopTime = time.time() + args['duration']
        comp = clientApi.GetEngineCompFactory().CreatePostProcess(self.mLevelId)
        comp.SetEnableLensStain(True)
        comp.SetLensStainIntensity(1)
        comp.SetLensStainColor(0, (255,255,255))
        comp.SetLensStainTexture("textures/sfxs/only_white")

    @Listener.Server('onSmoke')
    def onSmoke(self, args):
        size = 4.5
        x, y, z = args['pos']
        posList = [(x + size, y + size, z), (x - size, y + size, z), (x, y + size, z + size), (x, y + size, z - size), (x + size, y - size, z), (x - size, y - size, z), (x, y - size, z + size), (x, y - size, z - size)]
        rotList = [(0, 90, 0), (0, 270, 0), (0, 0, 0), (0, 180, 0), (180, 90, 0), (180, 270, 0), (180, 0, 0), (180, 180, 0)]
        obj = {
            'createTime': time.time(),
            'destroyTime': time.time() + args['duration'],
            'feid': [],
        }
        for pos, rot in zip(posList, rotList):
            frameEntityId = self.CreateEngineSfxFromEditor("effects/smoke.json")
            print ("CreateEngineSfxFromEditor", frameEntityId)
            frameAniTransComp = clientApi.GetEngineCompFactory().CreateFrameAniTrans(frameEntityId)
            frameAniTransComp.SetPos(pos)
            frameAniTransComp.SetRot(rot)
            frameAniTransComp.SetScale((size, size, size))
            frameAniControlComp = clientApi.GetEngineCompFactory().CreateFrameAniControl(frameEntityId)
            frameAniControlComp.Play()
            obj['feid'].append(frameEntityId)
        self.smokes.append(obj)


    @Listener.Engine(Event.OnScriptTickClient)
    def OnScriptTickClient(self):
        tim = time.time()
        if self.flashStopTime - tim > 1:
            comp = clientApi.GetEngineCompFactory().CreatePostProcess(self.mLevelId)
            comp.SetLensStainIntensity(1)
        elif self.flashStopTime - tim > 0:
            comp = clientApi.GetEngineCompFactory().CreatePostProcess(self.mLevelId)
            comp.SetLensStainIntensity((self.flashStopTime - tim) ** 0.28)
        else:
            comp = clientApi.GetEngineCompFactory().CreatePostProcess(self.mLevelId)
            comp.SetEnableLensStain(False)

        for smoke in self.smokes:
            if smoke['destroyTime'] < time.time():
                for entityId in smoke['feid']:
                    self.DestroyEntity(entityId)
                self.smokes.remove(smoke)

    def Destroy(self):
        Listener.UnListenForEvent(self)