[返回](../README.md)
# ButtonBoard 
- 通用按钮面板插件，封装客户端UI操作
- *本插件全部方法已集成至Player类中
----
## 接口
### **newButtonBoard**
- 实例化一个文字面板(单例)
- 返回ButtonBoard实例，并将player.bb设置为此实例

|参数|类型|说明|
|:---:|:---:|:---:|
|*title|str|主标题|
|*subtitle|str|上方小标题|

```python
bb = player.newButtonBoard('大标题','上方小文字')
```
## Class - ButtonBoard(player.bb)
### **newRender**
- 生成一个Render字典

|参数|类型|说明|
|:---:|:---:|:---:|
|type|str|显示类型{'text','item','image'}|
|content|str|内容{text,id,path}|
|*num|str/int|右下角角标|
|*aux|int|物品附加值, 仅当type=='item'时候有效|
|*enchant|bool|物品是否附魔, 仅当type=='item'时候有效|

```python
bb.newRender('item','minecraft:grass',6,0,True)
```

### **newButton**
- 生成一个按钮字典

|参数|类型|说明|
|:---:|:---:|:---:|
|text|str|按钮中心显示文本|
|*renderL|dict|按钮左侧render|
|*renderR|dict|按钮右侧render|
|*textFormat|list|按钮文字格式化文本列表|
|*waitRes|bool|按钮再次触发是否等待上次响应完成|
|*buttonId|str|按钮唯一Id(默认随机uuid)|
|*callback|func|按钮回调函数|
|*args|dict|按钮回调函数参数|

```python
bb.newButton('>> {} <<\n金币: {}  点券: {}',textFormat=['商品名字',10,20],waitRes=True,callback=self.goodsClick,args={'gold':10,'paper':20}),
```

### **setButton**
- 设置并重载一个按钮
- (若所属页面cache设置为False会导致刷新后被setPage接口覆盖)

|参数|类型|说明|
|:---:|:---:|:---:|
|buttonId_or_button|str/dict|按钮唯一Id或按钮字典|
|*text|str|按钮中心显示文本|
|*renderL|dict|按钮左侧render|
|*renderR|dict|按钮右侧render|
|*textFormat|list|按钮文字格式化文本列表|
|*waitRes|bool|按钮再次触发是否等待上次响应完成|
|*callback|func|按钮回调函数|
|*args|dict|按钮回调函数参数|

```python
bb.setButton('goods2','>> {} <<\n金币: {}  点券: {}',textFormat=['商品名字',10,20],waitRes=True,callback=self.goodsClick,args={'gold':10,'paper':20}),
```

### **newPage**
- 注册一个页面, 当客户端点击左侧按钮加载时触发回调函数

|参数|类型|说明|
|:---:|:---:|:---:|
|pageId|str|页面唯一Id|
|*callback|func|客户端请求加载回调函数|


```python
bb.newPage('goodsList',self.getGoodsList)
```

### **setPage**
- 设置页面内容(通常在newPage接口的回调函数中使用)

|参数|类型|说明|
|:---:|:---:|:---:|
|pageId|str|页面唯一Id|
|*content|str|页面简介文字(支持多行)|
|*buttonList|list|按钮列表|
|*title|str|本页面的大标题(默认为初始化时的title)|
|*subtitle|str|本页面的小标题(默认为初始化时的subtitle)|
|*backId|str|左上角返回按钮链接page(二级菜单) (默认为None，不显示返回按钮)|
|*cache|bool|是否缓存|


```python
bb.setPage('goodsList','商品列表如下\n请选择:',[button,button...])
```


### **changePage**
- 更改某个父栏显示的pageId(常用于二级菜单)

|参数|类型|说明|
|:---:|:---:|:---:|
|pageName|str|父栏名称|
|pageId|str|页面唯一Id|

```python
bb.changePage('商品列表','ConfirmGoodsButtonClick')
```

### **setLoad**
- 设置面板是否处于加载
- 使用需注意加载后调用setLoad(False)，否则含有waitRes按钮可能会无法正常点击

|参数|类型|说明|
|:---:|:---:|:---:|
|isLoad|bool|是否加载|

```python
bb.setLoad(False)
```

### **alert**
- 面板下方提示文字，使用后将弹出并展示3秒

|参数|类型|说明|
|:---:|:---:|:---:|
|text|str|提示文字|

```python
bb.alert('购买成功')
```

### **send**
- 使玩家打开面板

|参数|类型|说明|
|:---:|:---:|:---:|
|pages|list[list[Render(dict),text(str),pageId(str)],]|页面映射关系|
|*initPages|dict{pageId(str):page(dict),}|初始化客户端cache|
|*select|str|初始化选择父栏名称|
```python
bb.send([
    [bb.newRender('item','minecraft:iron_sword',66),'测试左栏1','testPage1'],
    [bb.newRender('item','minecraft:golden_sword',11),'测试左栏2','testPage2']
])
```

### **close**
- 强制关闭本面板

```python
bb.close()
```