[返回](../README.md)
# HotbarMenu
- 物品栏菜单插件
- *本插件全部方法已集成至Player类中
---
## 事件
### **HotbarMenuFirstClick**
- 玩家单击/手柄/滚轮/数字按键第一次选择时触发

|参数|类型|说明|
|:---:|:---:|:---:|
|playerId|str|点击按钮的玩家pid|
|item|dict|玩家所点击的物品类|
|tag|int|在addMenuItem中设置的唯一标识|
|show|int|点击后显示的actionbar的文字,可以更改或置为False|

```python
@Listener.Custom('HotbarMenuFirstClick')
def HotbarMenuFirstClick(self,args):
    player = Pl(args['playerId'])
```

### **HotbarMenuDoubleClick**
- 玩家双击/长按/右键确认选择时触发

|参数|类型|说明|
|:---:|:---:|:---:|
|playerId|str|点击按钮的玩家pid|
|item|dict|玩家所点击的物品类|
|tag|int|在addMenuItem中设置的唯一标识|

```python
@Listener.Custom('HotbarMenuDoubleClick')
def HotbarMenuDoubleClick(self,args):
    player = Pl(args['playerId'])
```
----
## 接口
### **addMenuItem**
- 向玩家的物品栏中生成一个菜单物品

|参数|类型|说明|
|:---:|:---:|:---:|
|item|Item|物品类(会有部分字段被覆盖)|
|text|str|首次点击后再actionbar显示的内容|
|tag|str|唯一标识(在事件中的返回值)|
|slot|int|Hotbar槽位 [0,8]|

```python
player.addMenuItem(Item('minecraft:log',count=6,name='§e§l测试菜单功能物品1'),slot=0,tag='1')
```

