{"format_version": "1.13.0", "minecraft:entity": {"components": {"minecraft:collision_box": {"height": 0.01, "width": 0.01}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_dropped_ticks": 7, "max_optimized_distance": 80.0, "use_motion_prediction_hints": true}}, "minecraft:hurt_on_condition": {"damage_conditions": [{"cause": "lava", "damage_per_tick": 4, "filters": {"operator": "==", "subject": "self", "test": "in_lava", "value": true}}]}, "minecraft:persistent": {}, "minecraft:physics": {"has_collision": true, "has_gravity": true}, "minecraft:projectile": {"anchor": 1, "gravity": 0.08, "hit_sound": "bow", "offset": [0, -0.1, 0], "on_hit": {"arrow_effect": {"apply_effect_to_blocking_targets": false}, "impact_damage": {"damage": 0, "destroy_on_hit": false, "knockback": false, "max_critical_damage": 10, "min_critical_damage": 9, "power_multiplier": 0.97, "semi_random_diff_damage": false}, "stick_in_ground": {"shake_time": 0.35}}, "power": 2.11, "should_bounce": true, "shouldbounce": true, "uncertainty_base": 1, "uncertainty_multiplier": 0}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:type_family": {"family": ["inanimate"]}, "netease:custom_entity_type": {"value": "projectile_entity"}, "netease:pick_up": {"item_name": "zmqy:smoke"}}, "description": {"identifier": "zmqy:smoke", "is_spawnable": false, "is_summonable": true}}}