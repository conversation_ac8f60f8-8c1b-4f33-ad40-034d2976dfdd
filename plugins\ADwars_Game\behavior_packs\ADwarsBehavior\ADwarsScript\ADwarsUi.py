# -*- coding: utf-8 -*-
import time
import Client<PERSON>pi as api
import mod.client.extraClient<PERSON>pi as clientApi
from mod.client.ui.screenNode import ScreenNode as SN
from mod_log import logger as log

ScreenNode = clientApi.GetScreenNodeCls()
ViewBinder = clientApi.GetViewBinderCls()
ViewRequest = clientApi.GetViewViewRequestCls()
CustomUIScreenProxy = clientApi.GetUIScreenProxyCls()
Root = api.RootPath


class ADwarsScreen(ScreenNode):
    def __init__(self, namespace, name, param):
        ScreenNode.__init__(self, namespace, name, param)
        self.mPlayerId = clientApi.GetLocalPlayerId()
        self.mLevelId = clientApi.GetLevelId()
        self.data = param.get('data',{})
        self.client = param.get('client',None)
        
    def Create(self):
        print('=====> ADwarsUi Created <=====')
    
    def OnActive(self):
        pass
    
    def OnDeactive(self):
        pass
    
    def Update(self):
        pass
    
    def Destroy(self):
        print('=====> ADwarsUi Destroyed <=====')


class ADwarsProxy(CustomUIScreenProxy):
    def __init__(self, screenName, screenNode):
        CustomUIScreenProxy.__init__(self, screenName, screenNode)
        self.ui = self.GetScreenNode() # type: SN

    def OnCreate(self):
        print("=====> ADwarsProxy Create <=====")

    def OnDestroy(self):
        print("=====> ADwarsProxy Destroyed <=====")

    def OnTick(self):
        pass
