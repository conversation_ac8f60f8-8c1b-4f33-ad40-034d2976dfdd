# -*- coding: utf-8 -*-

from common.mod import Mod
import server.extraServerApi as serverApi

@Mod.Binding(name='ADwars_1Mod', version='1.0.0')
class ADwars_1Server(object):
    
    @Mod.InitServer()
    def ADwars_1ServerInit(self):
        serverApi.RegisterSystem('ADwars_1Mod', 'ADwars_1ServerSystem', 'ADwars_1Script.ADwars_1ServerSystem.ADwars_1ServerSystem')

    @Mod.DestroyServer()
    def ADwars_1ServerDestroy(self):
        pass
