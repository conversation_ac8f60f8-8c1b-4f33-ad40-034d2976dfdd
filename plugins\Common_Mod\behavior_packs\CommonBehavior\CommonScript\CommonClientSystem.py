# -*- coding: utf-8 -*-
import mod.client.extraClient<PERSON>pi as clientApi
import logging
import Client<PERSON>pi as api

clientApi.SetKeepResourceWhenTransfer(False)
ClientSystem = clientApi.GetClientSystemCls()
NativeScreenManager = clientApi.GetNativeScreenManagerCls()
BCSys = ClientSystem('common','client')

class CommonClientSystem(ClientSystem):
    def __init__(self, namespace, systemName):
        ClientSystem.__init__(self, namespace, systemName)
        logging.info('=====> Common Init <=====')
        NS = clientApi.GetEngineNamespace()
        ES = clientApi.GetEngineSystemName()
        self.mPlayerId = clientApi.GetLocalPlayerId()
        self.mLevelId = clientApi.GetLevelId()
        self.ListenForEvent(NS,ES,'OnScriptTickClient',self,self.OnScriptTickClient)
        self.ListenForEvent(NS,ES,'UiInitFinished',self,self.UiInitFinished)
        self.ListenForEvent(NS,ES,'OnLocalPlayerStopLoading',self,self.OnLocalPlayerStopLoading)
        self.ListenForEvent(NS,ES,'ClientBlockUseEvent',self,self.ClientBlockUseEvent)
        self.ListenForEvent(NS,ES,'PlayerTryDropItemClientEvent',self,self.PlayerTryDropItemClientEvent)
        self.ListenForEvent(NS,ES,'OnKeyPressInGame',self,self.OnKeyPressInGame)
        self.ListenForEvent(NS,ES,'DimensionChangeFinishClientEvent',self,self.DimensionChangeFinishClientEvent)
        self.ListenForEvent(NS,ES,'PlayerAttackEntityEvent',self,self.PlayerAttackEntityEvent)
        self.ListenForEvent('common','game','CreateEmitter',self,self.CreateEmitter)
        self.ListenForEvent("neteaseShop", "neteaseShopBeh", "BuyItemPaySuccessEvent", self, self.OnBuyItemPaySuccess)
        self.tick = 0
        self.isBlur = False
        self.run_on = True
        self.banMix = True

    def PlayerAttackEntityEvent(self, args):
        victimId = args['victimId']
        comp = clientApi.GetEngineCompFactory().CreatePos(victimId)
        entityFootPos = comp.GetFootPos()
        comp = clientApi.GetEngineCompFactory().CreateParticleSystem(self.mPlayerId)
        comp.Create('zmqy:damage_emitter', (entityFootPos[0], entityFootPos[1] + 1.7, entityFootPos[2]))

    def OnBuyItemPaySuccess(self, data):
        self.NotifyToServer('BuyItemPaySuccess', {})

    def DimensionChangeFinishClientEvent(self, args):
        comp = clientApi.GetEngineCompFactory().CreateSkyRender(self.mLevelId)
        comp.SetSkyTextures(list(['textures/environment/overworld_cubemap/cubemap_' + str(i) for i in range(6)]))

    def UiInitFinished(self, args):
        self.DimensionChangeFinishClientEvent(1)
        # clientApi.RegisterUI('CommonMod', 'uiName', "CommonScript.CommonUi.CommonScreen", "uiName.main")
        # clientApi.CreateUI('CommonMod', 'uiName', {"isHud": 1})
        if self.banMix:
            print (NativeScreenManager.instance().RegisterScreenProxy("crafting.inventory_screen", "CommonScript.CommonUi.InvScreenProxy"))
            print (NativeScreenManager.instance().RegisterScreenProxy("crafting_pocket.inventory_screen_pocket", "CommonScript.CommonUi.InvPocketScreenProxy"))
            print (NativeScreenManager.instance().RegisterScreenProxy("crafting_pocket.inventory_screen_pocket", "CommonScript.CommonUi.InvPocketScreenProxy"))

    def OnKeyPressInGame(self, args):
        comp = clientApi.GetEngineCompFactory().CreateActorMotion(self.mPlayerId)
        if args['screenName'] == 'hud_screen':
            if args['key'] == '87' and self.run_on: # W
                if args['isDown'] == '1':
                    comp.BeginSprinting()
                else:
                    comp.EndSprinting()
            if args['key'] == '73': # I
                if args['isDown'] == '1':
                    self.run_on = not self.run_on
                    comp = clientApi.GetEngineCompFactory().CreateGame(self.mPlayerId)
                    comp.SetPopupNotice('§7使用[I]键进行开关设置', "§a强制疾跑已开启" if self.run_on else '§c强制疾跑已关闭')

    def OnScriptTickClient(self):
        ui = clientApi.GetTopUI()
        if ui != "hud_screen":
            if not self.isBlur:
                self.isBlur = True
                comp = clientApi.GetEngineCompFactory().CreatePostProcess(self.mLevelId)
                comp.SetEnableGaussianBlur(True)
                comp.SetGaussianBlurRadius(1.5)
        else:
            if self.isBlur:
                self.isBlur = False
                comp = clientApi.GetEngineCompFactory().CreatePostProcess(self.mLevelId)
                comp.SetEnableGaussianBlur(False)    
        
    def ClientBlockUseEvent(self,args):
        pass
    
    def PlayerTryDropItemClientEvent(self,args):
        pass
    
    def OnLocalPlayerStopLoading(self,args):
        BCSys.NotifyToServer('ClientLoaded',args)
    
    def CreateEmitter(self,args):
        comp = clientApi.GetEngineCompFactory().CreateParticleSystem(self.mPlayerId)
        comp.Create(args['eid'], args['pos'])
        # comp.Remove(parId)
        # pass
    
    def Destroy(self):
        logging.info('=====> Common Destroy <=====')